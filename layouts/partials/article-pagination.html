{{ if .Params.showPagination | default (.Site.Params.article.showPagination | default true) }}
  {{ if or .NextInSection .PrevInSection }}
    {{ $next := .NextInSection }}
    {{ $prev := .PrevInSection }}
    {{ if .Params.invertPagination | default (.Site.Params.article.invertPagination | default false) }}
      {{ $next = .PrevInSection }}
      {{ $prev = .NextInSection }}
    {{ end }}
    <nav class="pt-8" aria-label="Article navigation">
      <hr class="border-dotted border-neutral-300 dark:border-neutral-600 mb-6">

      {{ $hasOnlyOne := and (or $prev $next) (not (and $prev $next)) }}
      {{ if $hasOnlyOne }}
        <!-- 只有一个导航项时的居中布局 -->
        <div class="flex justify-center">
          {{ if $prev }}
            <div class="w-full max-w-md">
              <a class="group block p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200" href="{{ $prev.RelPermalink }}">
                <div class="flex items-start gap-3">
                  <span class="flex-shrink-0 mt-1 text-neutral-500 group-hover:text-primary-600 dark:text-neutral-400 dark:group-hover:text-primary-400 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </span>
                  <div class="min-w-0 flex-1">
                    <div class="text-xs uppercase tracking-wide text-neutral-500 dark:text-neutral-400 mb-1">
                      {{ i18n "article.previous" | default "Previous" }}
                    </div>
                    <h3 class="font-medium text-neutral-900 dark:text-neutral-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors leading-snug mb-2" title="{{ $prev.Title | plainify }}">
                      {{ $prev.Title | emojify | truncate 60 "..." }}
                    </h3>
                    {{ if .Params.showDate | default (.Site.Params.article.showDate | default true) }}
                      <div class="text-xs text-neutral-500 dark:text-neutral-400">
                        {{ partial "meta/date.html" $prev.Date }}
                      </div>
                    {{ end }}
                  </div>
                </div>
              </a>
            </div>
          {{ else }}
            <div class="w-full max-w-md">
              <a class="group block p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200" href="{{ $next.RelPermalink }}">
                <div class="flex items-start gap-3">
                  <div class="min-w-0 flex-1 text-center">
                    <div class="text-xs uppercase tracking-wide text-neutral-500 dark:text-neutral-400 mb-1">
                      {{ i18n "article.next" | default "Next" }}
                    </div>
                    <h3 class="font-medium text-neutral-900 dark:text-neutral-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors leading-snug mb-2" title="{{ $next.Title | plainify }}">
                      {{ $next.Title | emojify | truncate 60 "..." }}
                    </h3>
                    {{ if .Params.showDate | default (.Site.Params.article.showDate | default true) }}
                      <div class="text-xs text-neutral-500 dark:text-neutral-400">
                        {{ partial "meta/date.html" $next.Date }}
                      </div>
                    {{ end }}
                  </div>
                  <span class="flex-shrink-0 mt-1 text-neutral-500 group-hover:text-primary-600 dark:text-neutral-400 dark:group-hover:text-primary-400 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </span>
                </div>
              </a>
            </div>
          {{ end }}
        </div>
      {{ else }}
        <!-- 两个导航项时的网格布局 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 上一篇文章 -->
          <div class="order-1">
            <a class="group block p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 h-full" href="{{ $prev.RelPermalink }}">
              <div class="flex items-start gap-3 h-full">
                <span class="flex-shrink-0 mt-1 text-neutral-500 group-hover:text-primary-600 dark:text-neutral-400 dark:group-hover:text-primary-400 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </span>
                <div class="min-w-0 flex-1">
                  <div class="text-xs uppercase tracking-wide text-neutral-500 dark:text-neutral-400 mb-1">
                    {{ i18n "article.previous" | default "Previous" }}
                  </div>
                  <h3 class="font-medium text-neutral-900 dark:text-neutral-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors leading-snug mb-2" title="{{ $prev.Title | plainify }}">
                    {{ $prev.Title | emojify | truncate 50 "..." }}
                  </h3>
                  {{ if .Params.showDate | default (.Site.Params.article.showDate | default true) }}
                    <div class="text-xs text-neutral-500 dark:text-neutral-400">
                      {{ partial "meta/date.html" $prev.Date }}
                    </div>
                  {{ end }}
                </div>
              </div>
            </a>
          </div>

          <!-- 下一篇文章 -->
          <div class="order-2">
            <a class="group block p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 h-full" href="{{ $next.RelPermalink }}">
              <div class="flex items-start gap-3 h-full">
                <div class="min-w-0 flex-1 text-right">
                  <div class="text-xs uppercase tracking-wide text-neutral-500 dark:text-neutral-400 mb-1">
                    {{ i18n "article.next" | default "Next" }}
                  </div>
                  <h3 class="font-medium text-neutral-900 dark:text-neutral-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors leading-snug mb-2" title="{{ $next.Title | plainify }}">
                    {{ $next.Title | emojify | truncate 50 "..." }}
                  </h3>
                  {{ if .Params.showDate | default (.Site.Params.article.showDate | default true) }}
                    <div class="text-xs text-neutral-500 dark:text-neutral-400">
                      {{ partial "meta/date.html" $next.Date }}
                    </div>
                  {{ end }}
                </div>
                <span class="flex-shrink-0 mt-1 text-neutral-500 group-hover:text-primary-600 dark:text-neutral-400 dark:group-hover:text-primary-400 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </span>
              </div>
            </a>
          </div>
        </div>
      {{ end }}
    </nav>
  {{ end }}
{{ end }}
