# -- Theme Options --
# These options control how the theme functions and allow you to
# customise the display of your website.
#
# Refer to the theme docs for more details about each of these parameters.
# https://blowfish.page/docs/configuration/#theme-parameters

colorScheme = "github"
defaultAppearance = "light" # valid options: light or dark
autoSwitchAppearance = true

enableSearch = true
enableCodeCopy = true

# mainSections = ["section1", "section2"]
# robots = ""

# defaultBackgroundImage = "IMAGE.jpg" # used as default for background images 
# defaultFeaturedImage = "IMAGE.jpg" # used as default for featured images in all articles
# defaultSocialImage = "/android-chrome-512x512.png" # used as default for social media sharing (Open Graph and Twitter)

# highlightCurrentMenuArea = true
smartTOC = true
# smartTOCHideUnfocusedChildren = true

fingerprintAlgorithm = "sha512" # Valid values are "sha512" (default), "sha384", "sha256"

giteaDefaultServer = "https://git.fsfe.org"
forgejoDefaultServer = "https://v11.next.forgejo.org"

[header]
  layout = "basic" # valid options: basic, fixed, fixed-fill, fixed-gradient, fixed-fill-blur

[footer]
  showMenu = true
  showCopyright = true
  showThemeAttribution = true
  showAppearanceSwitcher = true
  showScrollToTop = true

[homepage]
  layout = "profile" # valid options: page, profile, hero, card, background, custom
  showRecent = true
  showRecentItems = 6
  showMoreLink = true
  showMoreLinkDest = "/posts/"
  cardView = true
  cardViewScreenWidth = false
  layoutBackgroundBlur = false # only used when layout equals background
  disableHeroImageFilter = false # only used when layout equals hero

[article]
  showDate = true
  showViews = false
  showLikes = false
  showDateOnlyInArticle = false
  showDateUpdated = false
  showAuthor = false
  # showAuthorBottom = false
  showHero = false
  # heroStyle = "basic" # valid options: basic, big, background, thumbAndBackground
  layoutBackgroundBlur = false # only used when heroStyle equals background or thumbAndBackground
  layoutBackgroundHeaderSpace = false # only used when heroStyle equals background
  showBreadcrumbs = false
  showDraftLabel = true
  showEdit = false
  # editURL = "https://github.com/username/repo/"
  editAppendPath = true
  seriesOpened = false
  showHeadingAnchors = true
  showPagination = true
  invertPagination = false
  showReadingTime = true
  showTableOfContents = true
  showRelatedContent = false
  # relatedContentLimit = 3
  showTaxonomies = true # use showTaxonomies OR showCategoryOnly, not both
  showCategoryOnly = false # use showTaxonomies OR showCategoryOnly, not both
  showAuthorsBadges = false
  showWordCount = true
  # sharingLinks = [ "linkedin", "twitter", "bluesky", "mastodon", "reddit", "pinterest", "facebook", "email", "whatsapp", "telegram", "line"]
  showZenMode = false

[list]
  showHero = false
  # heroStyle = "background" # valid options: basic, big, background, thumbAndBackground
  layoutBackgroundBlur = false # only used when heroStyle equals background or thumbAndBackground
  layoutBackgroundHeaderSpace = false # only used when heroStyle equals background
  showBreadcrumbs = false
  showSummary = false
  showViews = false
  showLikes = false
  showTableOfContents = false
  showCards = false
  orderByWeight = false
  groupByYear = true
  cardView = false
  cardViewScreenWidth = false
  constrainItemsWidth = false

[sitemap]
  excludedKinds = ["taxonomy", "term"]

[taxonomy]
  showTermCount = true
  showHero = false
  # heroStyle = "background" # valid options: basic, big, background, thumbAndBackground
  showBreadcrumbs = false
  showViews = false
  showLikes = false
  showTableOfContents = false
  cardView = false

[term]
  showHero = false
  # heroStyle = "background" # valid options: basic, big, background, thumbAndBackground
  showBreadcrumbs = false
  showViews = false
  showLikes = false
  showTableOfContents = true
  groupByYear = false
  cardView = false
  cardViewScreenWidth = false

[firebase]
  # apiKey = "XXXXXX"
  # authDomain = "XXXXXX"
  # projectId = "XXXXXX"
  # storageBucket = "XXXXXX"
  # messagingSenderId = "XXXXXX"
  # appId = "XXXXXX"
  # measurementId = "XXXXXX"

[fathomAnalytics]
  # site = "ABC12345"
  # domain = "llama.yoursite.com"

[umamiAnalytics]
  # websiteid = "ABC12345"
  # domain = "llama.yoursite.com"
  # dataDomains = "yoursite.com,yoursite2.com"
  # scriptName = ""
  # enableTrackEvent = true

[selineAnalytics]
  # token = "XXXXXX"
  # enableTrackEvent = true

[buymeacoffee]
  # identifier = ""
  # globalWidget = true
  # globalWidgetMessage = "Hello"
  # globalWidgetColor = "#FFDD00"
  # globalWidgetPosition = "Right"

[verification]
  # google = ""
  # bing = ""
  # pinterest = ""
  # yandex = ""
  # fediverse = ""

[rssnext]
  # feedId = ""
  # userId = ""

[advertisement]
  # adsense = ""
