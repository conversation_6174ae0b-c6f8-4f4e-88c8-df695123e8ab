disabled = true
languageCode = "en"
languageName = "English"
weight = 1
title = "<PERSON><PERSON><PERSON>'s Blog"

[params]
  displayName = "EN"
  isoCode = "en"
  rtl = false
  dateFormat = "2 January 2006"
  # logo = "img/logo.png"
  # secondaryLogo = "img/secondary-logo.png"
  description = "A powerful, lightweight blog built with <PERSON> and <PERSON><PERSON> theme"
  # copyright = "Copy, _right?_ :thinking_face:"

# [params.author]
#   name = "Your Name"
#   email = "<EMAIL>"
#   headline = "Welcome to my blog"
#   bio = "A passionate developer sharing thoughts and experiences."
#   links = [
#     { email = "mailto:<EMAIL>" },
#     { github = "https://github.com/yourusername" },
#     { twitter = "https://twitter.com/yourusername" }
#   ]

#     { amazon = "https://www.amazon.com/hz/wishlist/ls/wishlist-id" },
#     { apple = "https://www.apple.com" },
#     { blogger = "https://username.blogspot.com/" },
#     { bluesky = "https://bsky.app/profile/username" },
#     { codepen = "https://codepen.io/username" },
#     { dev = "https://dev.to/username" },
#     { discord = "https://discord.gg/invitecode" },
#     { dribbble = "https://dribbble.com/username" },
#     { facebook = "https://facebook.com/username" },
#     { flickr = "https://www.flickr.com/photos/username/" },
#     { foursquare = "https://foursquare.com/username" },
#     { github = "https://github.com/username" },
#     { gitlab = "https://gitlab.com/username" },
#     { google = "https://www.google.com/" },
#     { hashnode = "https://username.hashnode.dev" },
#     { instagram = "https://instagram.com/username" },
#     { itch-io = "https://username.itch.io" },
#     { keybase = "https://keybase.io/username" },
#     { kickstarter = "https://www.kickstarter.com/profile/username" },
#     { lastfm = "https://lastfm.com/user/username" },
#     { linkedin = "https://linkedin.com/in/username" },
#     { mastodon = "https://mastodon.instance/@username" },
#     { medium = "https://medium.com/username" },
#     { microsoft = "https://www.microsoft.com/" },
#     { orcid = "https://orcid.org/userid" },
#     { patreon = "https://www.patreon.com/username" },
#     { pinterest = "https://pinterest.com/username" },
#     { reddit = "https://reddit.com/user/username" },
#     { researchgate = "https://www.researchgate.net/profile/username" },
#     { slack = "https://workspace.url/team/userid" },
#     { snapchat = "https://snapchat.com/add/username" },
#     { soundcloud = "https://soundcloud.com/username" },
#     { spotify = "https://open.spotify.com/user/userid" },
#     { stack-overflow = "https://stackoverflow.com/users/userid/username" },
#     { steam = "https://steamcommunity.com/profiles/userid" },
#     { telegram = "https://t.me/username" },
#     { threads = "https://www.threads.net/@username" },
#     { tiktok = "https://tiktok.com/@username" },
#     { tumblr = "https://username.tumblr.com" },
#     { twitch = "https://twitch.tv/username" },
#     { twitter = "https://twitter.com/username" },
#     { x-twitter = "https://twitter.com/username" },
#     { whatsapp = "https://wa.me/phone-number" },
#     { youtube = "https://youtube.com/username" },
#     { ko-fi = "https://ko-fi.com/username" },
#     { codeberg = "https://codeberg.org/username"},
#   ]
