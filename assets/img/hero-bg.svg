<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5a67d8;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="grad2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background gradient -->
  <rect width="1920" height="1080" fill="url(#grad1)"/>
  <rect width="1920" height="1080" fill="url(#grad2)"/>
  
  <!-- Floating particles -->
  <circle cx="200" cy="200" r="4" fill="#ffffff" opacity="0.6" filter="url(#glow)">
    <animate attributeName="cy" values="200;180;200" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1700" cy="300" r="6" fill="#ffffff" opacity="0.4" filter="url(#glow)">
    <animate attributeName="cy" values="300;280;300" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="800" r="5" fill="#ffffff" opacity="0.5" filter="url(#glow)">
    <animate attributeName="cy" values="800;820;800" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1500" cy="900" r="7" fill="#ffffff" opacity="0.3" filter="url(#glow)">
    <animate attributeName="cy" values="900;880;900" dur="4.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="800" cy="200" r="3" fill="#ffffff" opacity="0.7" filter="url(#glow)">
    <animate attributeName="cy" values="200;220;200" dur="2.8s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1200" cy="600" r="4" fill="#ffffff" opacity="0.5" filter="url(#glow)">
    <animate attributeName="cy" values="600;580;600" dur="3.2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Geometric shapes -->
  <polygon points="100,100 120,80 140,100 120,120" fill="#ffffff" opacity="0.1" filter="url(#glow)">
    <animateTransform attributeName="transform" type="rotate" values="0 120 100;360 120 100" dur="20s" repeatCount="indefinite"/>
  </polygon>
  <polygon points="1800,800 1820,780 1840,800 1820,820" fill="#ffffff" opacity="0.08" filter="url(#glow)">
    <animateTransform attributeName="transform" type="rotate" values="0 1820 800;-360 1820 800" dur="25s" repeatCount="indefinite"/>
  </polygon>
  
  <!-- Wave patterns -->
  <path d="M0,540 Q480,520 960,540 T1920,540 L1920,1080 L0,1080 Z" fill="#ffffff" opacity="0.05"/>
  <path d="M0,580 Q480,560 960,580 T1920,580 L1920,1080 L0,1080 Z" fill="#ffffff" opacity="0.03"/>
</svg>
