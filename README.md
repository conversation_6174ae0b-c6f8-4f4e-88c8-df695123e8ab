# Zayn 的技术博客

[![GitLab CI/CD](https://gitlab.cpinnov.run/devops/blowfish-blog/badges/main/pipeline.svg)](https://gitlab.cpinnov.run/devops/blowfish-blog/-/pipelines)
[![GitLab Pages](https://gitlab.cpinnov.run/devops/blowfish-blog/badges/main/coverage.svg)](https://gitlab.cpinnov.run/devops/blowfish-blog/-/jobs)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

> 基于 Hugo 和 Blowfish 主题构建的个人技术博客，专注于容器技术、DevOps、云原生等领域的知识分享。

## 🌟 项目特色

- 🚀 **极速体验**: 采用 Hugo 静态网站生成器，页面加载速度极快
- 🎨 **视觉优雅**: 使用 Blowfish 主题，海洋风格配色，流畅动画效果
- 📱 **多端适配**: 完美支持桌面端、平板和移动端浏览
- 🔍 **搜索友好**: 内置 SEO 优化，支持全文搜索
- 📝 **内容丰富**: 支持代码高亮、图表展示、数学公式等多种内容格式
- 🌐 **多语言**: 支持中英文双语内容

## 🛠️ 技术栈

- **静态网站生成器**: [Hugo](https://gohugo.io/) v0.120+
- **主题**: [Blowfish](https://blowfish.page/) - 现代化的 Hugo 主题
- **部署**: GitLab Pages + GitLab CI/CD
- **内容管理**: Markdown + Front Matter
- **代码高亮**: Prism.js
- **图表支持**: Mermaid.js
- **数学公式**: KaTeX

## 📚 内容分类

本博客主要分享以下技术领域的内容：

- **容器技术**: Docker、Kubernetes、Rancher 等
- **DevOps 工具**: Git、Jenkins、GitLab、ArgoCD 等
- **云原生技术**: 微服务、服务网格、监控告警等
- **系统运维**: Linux 管理、网络配置、服务部署等
- **开发实践**: 编程技巧、工具使用、项目经验等

## 🚀 快速开始

### 环境要求

- Hugo Extended 版本 (推荐 v0.120+)
- Git
- Node.js (可选，用于主题开发)

### 本地开发

1. **克隆项目**
   ```bash
   <NAME_EMAIL>:devops/blowfish-blog.git
   cd blowfish-blog
   ```

2. **启动开发服务器**
   ```bash
   hugo server -D
   ```

3. **访问网站**
   打开浏览器访问 [http://localhost:1313](http://localhost:1313)

### 构建部署

1. **构建静态文件**
   ```bash
   hugo --minify
   ```

2. **构建产物**
   构建完成后，静态文件将生成在 `public/` 目录中

## 📁 项目结构

```
blowfish-blog/
├── content/                 # 内容目录
│   ├── posts/              # 博客文章
│   ├── about.md            # 关于页面
│   └── _index.md           # 首页内容
├── config/                 # 配置文件
│   └── _default/           # 默认配置
├── layouts/                # 自定义布局
├── static/                 # 静态资源
├── assets/                 # 资源文件
├── themes/                 # 主题目录
│   └── blowfish/           # Blowfish 主题
├── hugo.toml              # Hugo 主配置
└── README.md              # 项目说明
```

## ✍️ 写作指南

### 创建新文章

1. **使用 Hugo 命令创建**
   ```bash
   hugo new posts/your-article-title.md
   ```

2. **手动创建**
   在 `content/posts/` 目录下创建 `.md` 文件，包含以下 Front Matter：

   ```yaml
   ---
   title: "文章标题"
   description: "文章描述"
   date: 2024-01-01
   tags: ["标签1", "标签2"]
   categories: ["分类1"]
   draft: false
   ---
   ```

### 文章格式

- 使用 Markdown 语法编写
- 支持 Hugo 短代码和模板
- 图片存放在 `content/posts/assets/` 目录
- 代码块支持语法高亮

## 🔧 配置说明

### 主要配置项

- `baseURL`: 网站基础URL
- `languageCode`: 默认语言代码
- `title`: 网站标题
- `theme`: 使用的主题
- `buildDrafts`: 是否构建草稿
- `buildFuture`: 是否构建未来文章

### 主题配置

Blowfish 主题支持丰富的自定义配置，包括：

- 颜色主题
- 导航菜单
- 社交链接
- 评论系统
- 分析统计

详细配置请参考 [Blowfish 主题文档](https://blowfish.page/docs/configuration/)。

## 🚀 CI/CD 部署

项目使用 GitLab CI/CD 进行自动化部署：

- **触发条件**: 推送到 `main` 分支
- **构建环境**: GitLab Runner
- **部署目标**: GitLab Pages
- **构建产物**: `public/` 目录

### 部署流程

1. 代码推送到 GitLab
2. GitLab CI 自动触发构建
3. 安装 Hugo 并构建静态文件
4. 部署到 GitLab Pages
5. 自动更新网站

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [在线访问](https://www.treesir.pub/)
- [GitLab 仓库](https://gitlab.cpinnov.run/devops/blowfish-blog)
- [Hugo 官网](https://gohugo.io/)
- [Blowfish 主题](https://blowfish.page/)

## 📞 联系方式

- 作者: Zayn
- 邮箱: <EMAIL>
- 博客: [https://www.treesir.pub/](https://www.treesir.pub/)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
