# 部署说明

本文档详细说明如何配置和部署 Hugo 博客到 GitLab Pages。

## 🚀 GitLab Pages 部署

### 前置条件

1. **GitLab 仓库**: 确保项目已推送到 GitLab
2. **GitLab Runner**: 确保项目有可用的 GitLab Runner
3. **权限配置**: 确保有足够的权限进行 Pages 部署

### 自动部署配置

项目已配置了完整的 GitLab CI/CD 流水线，包括：

#### 1. 构建阶段 (`build`)
- 使用官方 Hugo Docker 镜像
- 构建生产环境的静态文件
- 启用 Git 信息集成
- 生成压缩优化的文件

#### 2. 部署阶段 (`pages`)
- 自动部署到 GitLab Pages
- 创建 `.nojekyll` 文件避免 Jekyll 处理
- 设置 30 天的构建产物保留期

#### 3. 测试阶段 (`test`)
- 验证 Hugo 配置
- 检查内容语法
- 允许失败（不影响主流程）

#### 4. 预览构建 (`build:preview`)
- 为合并请求提供预览
- 包含草稿和未来文章
- 1 小时构建产物保留期

### 部署流程

1. **推送代码到 main 分支**
   ```bash
   git add .
   git commit -m "Update content"
   git push origin main
   ```

2. **自动触发构建**
   - GitLab CI 自动检测到推送
   - 开始执行构建流水线
   - 构建 Hugo 静态文件

3. **自动部署到 Pages**
   - 构建成功后自动部署
   - 网站将在几分钟内更新

### 访问网站

部署成功后，网站将在以下地址可用：
- **主域名**: `https://your-username.gitlab.io/your-project-name/`
- **自定义域名**: 如果配置了自定义域名，则使用自定义域名

## 🔧 手动部署

如果需要手动部署，可以按以下步骤操作：

### 1. 本地构建

```bash
# 安装 Hugo (如果未安装)
# macOS
brew install hugo

# Linux
sudo apt-get install hugo

# 构建静态文件
hugo --minify --gc --enableGitInfo
```

### 2. 上传到服务器

```bash
# 使用 rsync 上传
rsync -avz --delete public/ user@server:/path/to/web/root/

# 或使用 scp
scp -r public/* user@server:/path/to/web/root/
```

## 🌐 自定义域名配置

### 1. 在 GitLab 中配置

1. 进入项目设置 → Pages
2. 添加自定义域名
3. 配置 SSL 证书（推荐）

### 2. DNS 配置

添加以下 DNS 记录：

```
# A 记录
your-domain.com  ->  *************

# CNAME 记录
www.your-domain.com  ->  your-username.gitlab.io
```

### 3. 验证配置

等待 DNS 传播后，访问自定义域名验证配置是否生效。

## 📊 监控和日志

### 查看构建日志

1. 进入 GitLab 项目
2. 点击 CI/CD → Pipelines
3. 查看最新的构建日志

### 常见问题排查

#### 构建失败

1. **Hugo 版本问题**
   - 检查 Hugo 版本兼容性
   - 更新到最新版本

2. **配置错误**
   - 检查 `hugo.toml` 配置
   - 验证主题配置

3. **内容错误**
   - 检查 Markdown 语法
   - 验证 Front Matter 格式

#### 部署失败

1. **权限问题**
   - 检查 GitLab Pages 权限
   - 确认 Runner 配置

2. **路径问题**
   - 检查构建产物路径
   - 验证 Pages 配置

## 🔒 安全配置

### 1. 环境变量

敏感信息应使用 GitLab 环境变量：

```bash
# 在 GitLab 项目设置中配置
HUGO_ENV=production
HUGO_ENABLEGITINFO=true
```

### 2. 访问控制

- 配置 Pages 访问权限
- 设置 IP 白名单（如需要）
- 启用 HTTPS 强制跳转

## 📈 性能优化

### 1. 构建优化

- 启用 Hugo 缓存
- 使用压缩选项
- 优化图片资源

### 2. 部署优化

- 配置 CDN
- 启用 Gzip 压缩
- 设置缓存策略

## 🆘 故障排除

### 常见错误

1. **"Hugo not found"**
   - 检查 Docker 镜像
   - 验证 Hugo 安装

2. **"Build timeout"**
   - 增加构建时间限制
   - 优化构建过程

3. **"Deployment failed"**
   - 检查 Pages 配置
   - 验证文件权限

### 获取帮助

- 查看 [GitLab Pages 文档](https://docs.gitlab.com/ee/user/project/pages/)
- 参考 [Hugo 部署指南](https://gohugo.io/hosting-and-deployment/)
- 提交 Issue 到项目仓库

---

如有问题，请参考项目 README 或提交 Issue。 