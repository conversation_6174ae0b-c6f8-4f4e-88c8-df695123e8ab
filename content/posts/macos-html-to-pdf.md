---
title: "<PERSON>os 一键 使用 wkhtmltopdf 将 html 转换为 pdf"
date: 2021-07-12T20:21:48+08:00
draft: false
tags: [ "wkhtmltopdf"]
tags_weight: 60
categories: ["unix"]
categories_weight: 30
keywords:
- unix
- wkhtmltopdf
- html
- pdf
- 转换
- 一键
description: "一键 使用 wkhtmltopdf 将 html 转换为 pdf"
---

# 安装 wkhtmltopdf

- **使用 brew 进行安装** 

  ```bash
  brew install --cask wkhtmltopdf
  ```

- 将目录下的所有 `html` 文件一键转换为 pdf 格式

  ```bash
  for f in *.html; do wkhtmltopdf --load-error-handling ignore -n --enable-local-file-access  $f "$f.pdf"; done
  ```

  

---



# 参考文档

- [https://segmentfault.com/a/1190000018988358](https://segmentfault.com/a/1190000018988358)

- https://github.com/wkhtmltopdf/wkhtmltopdf

