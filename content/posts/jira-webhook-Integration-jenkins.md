---
title: "<PERSON>ra Webhook Integration Jenkins"
date: 2021-03-16T08:49:04+08:00
draft: false
---



# <PERSON><PERSON> Webhook 与 Jenkins 集成

> `此文档为归档数据，暂不具备参考意义`

Jira 创建项目 并新建模块 关联 Gitlab 中对应仓库

![image-20210316104758799](https://cdn.treesir.pub/img/image-20210316104758799.png)

![image-20210316105052883](https://cdn.treesir.pub/img/image-20210316105052883.png)



Jenkins 创建 jira webhook manage pipeline

![image-20210316111226765](https://cdn.treesir.pub/img/image-20210316111226765.png)



jira 添加 网络钩子

```bash
http://jenkins.xxx.net/generic-webhook-trigger/invoke?token=jira-trigger-manage&runOpts=JiraPush&issueTypeName=null&issueTypeStatus=null&projectName=${project.key}
```

![image-20210316133740358](https://cdn.treesir.pub/img/image-20210316133740358.png)

