---
title: "使用 Docker 部署 GitLab 及常用优化配置"
date: 2021-03-04T16:31:06+08:00
draft: false
tags: [ "gitlab", "centos7"]
tags_weight: 60
categories: ["docker","docker-compose","devops"]
categories_weight: 60
keywords:
- Docker-Compose
- GitLab
- 安装部署
- DevOps
description: "使用 Docker 部署 GitLab 及常用运维优化配置说明"
---

# 环境要求

## 软件版本

- **GitLab 版本**：13.10.2-ce.0

  ```bash
  docker pull gitlab/gitlab-ce:13.10.2-ce.0
  ```

- **Docker Compose 版本**：1.18.0

  ```bash
  docker-compose --version
  docker-compose version 1.18.0, build 8dd22a9
  ```

- **Docker 版本**：19.03.9



# 部署 GitLab

## 创建 Docker Compose 配置文件

```bash
mkdir -p /data/docker-compose/gitlab-ce

cd /data/docker-compose/gitlab-ce # 注意保留配置文件，后续维护需要使用

cat docker-compose.yaml
version: '2'
services:
    gitlab:
      image: 'gitlab/gitlab-ce:13.10.2-ce.0'
      restart: always
      hostname: 'gitlab.treesir.pub'
      container_name: 'gitlab-ce'
      environment:
        TZ: 'Asia/Shanghai'
        GITLAB_OMNIBUS_CONFIG: |
          external_url 'http://gitlab.treesir.pub'  # or protocol "https"
          gitlab_rails['time_zone'] = 'Asia/Shanghai'
          gitlab_rails['smtp_enable'] = true
          gitlab_rails['smtp_address'] = "smtp.qq.com"
          gitlab_rails['smtp_port'] = 465
          gitlab_rails['smtp_user_name'] = "<EMAIL>"
          gitlab_rails['smtp_password'] = "sample"
          gitlab_rails['smtp_authentication'] = "login"
          gitlab_rails['smtp_enable_starttls_auto'] = true
          gitlab_rails['smtp_tls'] = true
          gitlab_rails['gitlab_email_from'] = '<EMAIL>'
      ports:
        - '18080:80'
      volumes:
        - /application/gitlab/config:/etc/gitlab
        - /application/gitlab/data:/var/opt/gitlab
        - /application/gitlab/logs:/var/log/gitlab
volumes:
    config:
    data:
    logs:

```

> **配置说明**：
> - 修改配置中的域名地址和 SMTP 信息
> - 示例使用 QQ 邮箱 SMTP，获取方式可参考[此方法](https://www.jianshu.com/p/9efaff9e9437)
> - 端口映射：将容器内的 80 端口映射到 18080，便于后续使用 Nginx 代理
> - 如无特殊需求，也可直接映射到 80 或 443 端口

## 启动服务

```bash
docker-compose up -d
```

![image-20210409163819513](https://cdn.treesir.pub/img/image-20210409163819513.png)

## 验证部署

等待几分钟后，访问对应端口进行测试：

> GitLab 默认管理员用户为 `root`

![image-20210409164819832](https://cdn.treesir.pub/img/image-20210409164819832.png)

> 服务启动完成。如果访问出现问题，可以查看容器日志：
>
> ```bash
> docker logs -f --tail 100 gitlab-ce
> ```





# 配置 Nginx 反向代理

![image-20210409165223276](https://cdn.treesir.pub/img/image-20210409165223276.png)

> 如上图所示，当容器端口映射不是 80 时，无法直接使用复制的地址。可以通过 Nginx 反向代理来解决这个问题。

## 安装 Nginx

```bash
yum install -y nginx*
```



## 配置 Nginx

创建 GitLab 代理配置文件：

```bash
cat /etc/nginx/conf.d/gitlab.conf

upstream gitlab {
  server *************:18080;  # 如果 Nginx 和 GitLab 在同一台机器上，建议使用 "127.0.0.1"
}

server {
  listen 80;
  server_name gitlab.treesir.pub;
  location / {
    proxy_read_timeout      300;
    proxy_connect_timeout   300;
    proxy_redirect          off;

    proxy_set_header        Host                $http_host;
    proxy_set_header        X-Real-IP           $remote_addr;
    proxy_set_header        X-Forwarded-For     $proxy_add_x_forwarded_for;
    proxy_set_header        X-Forwarded-Proto   http;
    proxy_set_header        X-Frame-Options     SAMEORIGIN;
    proxy_pass http://gitlab ;
  }
}

#server {
#  listen 443 ssl;
#  server_name registry.example.com;
#
#  ssl on;
#  ssl_certificate /letsencrypt/fullchain.pem;
#  ssl_certificate_key /letsencrypt/privkey.pem;
#
#  location / {
#    proxy_read_timeout      300;
#    proxy_connect_timeout   300;
#    proxy_redirect          off;
#
#    proxy_set_header        Host                $http_host;
#    proxy_set_header        X-Real-IP           $remote_addr;
#    proxy_set_header        X-Forwarded-For     $proxy_add_x_forwarded_for;
#    proxy_set_header        X-Forwarded-Proto   https;
#    proxy_set_header        X-Frame-Options     SAMEORIGIN;
#    proxy_pass http://gitlab;
#  }
#}

```

> **安全建议**：
> - 内网使用可仅开启 HTTP 协议
> - 公网部署建议启用 HTTPS，配置文件中已提供示例配置
> - 推荐使用 `acme.sh` 获取免费 SSL 证书

## 启动 Nginx

```bash
nginx -t
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful

nginx -s reload  # 重新加载配置

systemctl enable nginx # 设置开机自启
```



## 测试访问

修改 hosts 文件后，测试访问域名地址：

> [修改 hosts 文件方法说明](https://www.baidu.com/s?ie=utf-8&f=8&rsv_bp=1&rsv_idx=2&ch=&tn=baiduhome_pg&bar=&wd=%E6%93%8D%E4%BD%9C%E7%B3%BB%E7%BB%9F%E6%9B%B4%E6%94%B9hosts&rsv_spt=1&oq=%25E5%2590%2584%25E6%2593%258D%25E4%25BD%259C%25E7%25B3%25BB%25E7%25BB%9F%25E6%259B%25B4%25E6%2594%25B9hosts&rsv_pq=89a0cf6f00019b6e&rsv_t=01f6WSXkn%2BXRAo%2FEoqrourz%2FvvuGJcRuG5Kl8TxYOvSr2AF5gbkPOwyqVPpjoAO73UPK&rqlang=cn&rsv_enter=1&rsv_btype=t&rsv_dl=tb&inputT=962)

![image-20210409172159652](https://cdn.treesir.pub/img/image-20210409172159652.png)

## 功能测试

测试文件上传功能：

![image-20210409172547967](https://cdn.treesir.pub/img/image-20210409172547967.png)

![image-20210409172607523](https://cdn.treesir.pub/img/image-20210409172607523.png)

> 可以看到，刚才推送的文件已经在 Dashboard 中显示。





# GitLab 优化配置

> 以下优化项用于关闭 GitLab 中一些默认开启但实际用处不大的功能，可根据实际情况选择性配置，非必需操作。

## 关闭 Auto DevOps

默认新建项目时关闭 Auto DevOps 功能：

![image-20210412150002697](https://cdn.treesir.pub/img/image-20210412150002697.png)

## 启用 Webhook

与 Jenkins 集成时需要使用 Webhook，需要开启此功能：

![image-20210412150343435](https://cdn.treesir.pub/img/image-20210412150343435.png)

## 配置接口请求限制

开启后可有效防止 CC 攻击，公网部署建议开启，内网可选：

![image-20210412150549008](https://cdn.treesir.pub/img/image-20210412150549008.png)



## 启用 Grafana Dashboard

GitLab 内置 Prometheus 监控方案，启用 Grafana 后可访问 `${GITLAB_URL}/-/grafana` 查看监控数据：

![image-20210412150825760](https://cdn.treesir.pub/img/image-20210412150825760.png)

![image-20210412151147827](https://cdn.treesir.pub/img/image-20210412151147827.png)

> 默认的 Grafana 已预置相应的展示模板。

![image-20210412151401340](https://cdn.treesir.pub/img/image-20210412151401340.png)

## 配置时区本地化

![image-20210412151612643](https://cdn.treesir.pub/img/image-20210412151612643.png)

## 禁用 SSH 代码管理

![image-20210412152449920](https://cdn.treesir.pub/img/image-20210412152449920.png)



## 关闭注册功能

> 如果你开启了使用 ldap 用户认证的管理，此项强烈建议是把它关闭的。一般打开了的话，常见于一些对外服务的站点，企业中根本不需要。

![image-20210412152950209](https://cdn.treesir.pub/img/image-20210412152950209.png)



## 设置新用户最小权限

> 不勾选此项时，默认新添加的用户还是拥有`创建仓库` 的权限的，如果管理员想 `掌控全局` 的话，还是建议开启。

![image-20210412153256775](https://cdn.treesir.pub/img/image-20210412153256775.png)



# 总结

Gitlab 为企业中 常见的 git 代码管理软件，是在实施 `Devops` 过程中的利剑，功能强大，并内置了 ci/cd、repository、监控等功能。在对 Gitlab 进行配置优化时，我们需要注意在配置时还 存在着`全局生效配置` 和 `项目局部生效配置`，如在全局中配置了未见生效，可以去对应的项目组中查看此项目对应配置也做了相应的更改否。

