---
title: "企业级 GitLab 平台部署与运维完整指南"
date: 2021-03-04T16:31:06+08:00
draft: false
tags: [ "gitlab", "docker", "devops", "git", "cicd"]
tags_weight: 60
categories: ["docker","docker-compose","devops"]
categories_weight: 60
keywords:
- GitLab
- Docker
- Git
- DevOps
- CI/CD
- 代码管理
- 企业级部署
- 容器化
description: "从基础部署到企业级配置的 GitLab 平台完整部署指南，包含高可用配置、安全优化、监控告警等最佳实践"
---

# GitLab 平台简介

## 什么是 GitLab

GitLab 是一个基于 Web 的 DevOps 生命周期工具，提供了 Git 仓库管理、代码审查、问题跟踪、CI/CD 流水线和 Wiki 等功能。作为一个完整的 DevOps 平台，GitLab 帮助团队协作开发、测试和部署应用程序。

### 核心特性

- **Git 仓库管理**：完整的 Git 版本控制功能
- **CI/CD 流水线**：内置的持续集成和持续部署
- **代码审查**：Merge Request 和代码质量检查
- **项目管理**：Issue 跟踪、里程碑、看板
- **安全扫描**：SAST、DAST、依赖扫描
- **容器注册表**：内置 Docker 镜像仓库
- **监控告警**：应用性能监控和日志管理

### 版本对比

| 版本 | 特性 | 适用场景 |
|------|------|----------|
| **GitLab CE (Community Edition)** | 基础功能，免费开源 | 小团队、个人项目 |
| **GitLab EE (Enterprise Edition)** | 企业级功能，商业授权 | 大型企业、高级安全需求 |
| **GitLab.com** | SaaS 服务 | 快速启动、无需维护 |

## 架构设计

### 单机架构

```
┌─────────────────────────────────────────┐
│              GitLab 单机部署             │
├─────────────────────────────────────────┤
│  Web UI │ Git HTTP(S) │ Git SSH │ API  │
├─────────────────────────────────────────┤
│         GitLab Rails Application        │
├─────────────────────────────────────────┤
│  Sidekiq │ Gitaly │ Workhorse │ Shell  │
├─────────────────────────────────────────┤
│  PostgreSQL │ Redis │ Nginx │ Prometheus│
└─────────────────────────────────────────┘
```

### 高可用架构

```
┌─────────────────────────────────────────┐
│              Load Balancer              │
└─────────────┬───────────────────────────┘
              │
    ┌─────────┼─────────┐
    │         │         │
┌───▼───┐ ┌───▼───┐ ┌───▼───┐
│GitLab │ │GitLab │ │GitLab │
│Node 1 │ │Node 2 │ │Node 3 │
└───┬───┘ └───┬───┘ └───┬───┘
    │         │         │
    └─────────┼─────────┘
              │
┌─────────────▼─────────────────┐
│        Shared Services        │
├───────────────────────────────┤
│ PostgreSQL │ Redis │ Gitaly   │
│ (Primary)  │Cluster│ Cluster  │
└───────────────────────────────┘
```

# 环境准备

## 系统要求

### 硬件要求

| 用户数量 | CPU | 内存 | 存储 | 网络 |
|----------|-----|------|------|------|
| **1-100** | 4 核 | 8GB | 100GB | 1Gbps |
| **100-500** | 8 核 | 16GB | 500GB | 1Gbps |
| **500-1000** | 16 核 | 32GB | 1TB | 10Gbps |
| **1000+** | 32 核 | 64GB+ | 2TB+ | 10Gbps |

### 软件要求

| 组件 | 最低版本 | 推荐版本 | 说明 |
|------|----------|----------|------|
| **操作系统** | CentOS 7.6 | CentOS 8+ / Ubuntu 20.04+ | 64位系统 |
| **Docker** | 19.03.0 | 20.10.0+ | 容器运行时 |
| **Docker Compose** | 1.25.0 | 1.29.0+ | 容器编排工具 |
| **GitLab** | 13.0.0 | 15.0.0+ | 推荐使用最新 LTS 版本 |

### 网络端口规划

| 端口 | 协议 | 服务 | 说明 |
|------|------|------|------|
| 80 | TCP | HTTP | Web 界面访问 |
| 443 | TCP | HTTPS | 安全 Web 访问 |
| 22 | TCP | SSH | Git SSH 访问 |
| 5050 | TCP | Container Registry | Docker 镜像仓库 |
| 9090 | TCP | Prometheus | 监控数据收集 |

## 环境检查脚本

```bash
cat > check-gitlab-env.sh << 'EOF'
#!/bin/bash

echo "=== GitLab 环境检查脚本 ==="
echo "检查时间: $(date)"
echo

# 检查操作系统
echo "=== 系统信息 ==="
cat /etc/redhat-release 2>/dev/null || lsb_release -a 2>/dev/null
uname -a
echo

# 检查内存
echo "=== 内存信息 ==="
free -h
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
if [ $TOTAL_MEM -lt 4096 ]; then
    echo "⚠ 警告: 内存不足 4GB，可能影响 GitLab 性能"
else
    echo "✓ 内存充足"
fi
echo

# 检查磁盘空间
echo "=== 磁盘空间 ==="
df -h
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "⚠ 警告: 磁盘使用率超过 80%"
else
    echo "✓ 磁盘空间充足"
fi
echo

# 检查 Docker
echo "=== Docker 环境检查 ==="
if command -v docker >/dev/null 2>&1; then
    docker --version
    echo "✓ Docker 已安装"

    if systemctl is-active --quiet docker; then
        echo "✓ Docker 服务运行正常"
    else
        echo "✗ Docker 服务未运行"
    fi
else
    echo "✗ Docker 未安装"
fi
echo

# 检查 Docker Compose
echo "=== Docker Compose 检查 ==="
if command -v docker-compose >/dev/null 2>&1; then
    docker-compose --version
    echo "✓ Docker Compose 已安装"
else
    echo "✗ Docker Compose 未安装"
fi
echo

# 检查网络端口
echo "=== 端口检查 ==="
for port in 80 443 22; do
    if netstat -tlnp | grep :$port >/dev/null 2>&1; then
        echo "⚠ 端口 $port 已被占用"
        netstat -tlnp | grep :$port
    else
        echo "✓ 端口 $port 可用"
    fi
done
echo

# 检查防火墙状态
echo "=== 防火墙状态 ==="
if systemctl is-active --quiet firewalld; then
    echo "防火墙已启用"
    firewall-cmd --list-ports
elif systemctl is-active --quiet ufw; then
    echo "UFW 防火墙已启用"
    ufw status
else
    echo "防火墙未启用"
fi
echo

echo "=== 环境检查完成 ==="
EOF

chmod +x check-gitlab-env.sh
./check-gitlab-env.sh
```



# GitLab 部署实施

## 方案一：Docker Compose 部署（推荐）

### 步骤 1：创建项目目录

```bash
# 创建项目目录
mkdir -p /data/gitlab && cd /data/gitlab

# 创建数据目录
mkdir -p {config,data,logs,backups}

# 设置目录权限
chmod -R 755 /data/gitlab
```

### 步骤 2：基础配置文件

```bash
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gitlab:
    image: gitlab/gitlab-ce:15.11.0-ce.0
    container_name: gitlab-ce
    restart: unless-stopped
    hostname: gitlab.your-domain.com

    environment:
      TZ: 'Asia/Shanghai'
      GITLAB_OMNIBUS_CONFIG: |
        # 基础配置
        external_url 'https://gitlab.your-domain.com'
        gitlab_rails['time_zone'] = 'Asia/Shanghai'

        # 邮件配置
        gitlab_rails['smtp_enable'] = true
        gitlab_rails['smtp_address'] = "smtp.your-domain.com"
        gitlab_rails['smtp_port'] = 587
        gitlab_rails['smtp_user_name'] = "<EMAIL>"
        gitlab_rails['smtp_password'] = "your-smtp-password"
        gitlab_rails['smtp_domain'] = "your-domain.com"
        gitlab_rails['smtp_authentication'] = "login"
        gitlab_rails['smtp_enable_starttls_auto'] = true
        gitlab_rails['smtp_tls'] = false
        gitlab_rails['gitlab_email_from'] = '<EMAIL>'
        gitlab_rails['gitlab_email_reply_to'] = '<EMAIL>'

        # 性能优化
        gitlab_rails['env'] = {
          'MALLOC_CONF' => 'dirty_decay_ms:1000,muzzy_decay_ms:1000'
        }

        # 备份配置
        gitlab_rails['backup_keep_time'] = 604800
        gitlab_rails['backup_path'] = "/var/opt/gitlab/backups"

        # 监控配置
        prometheus['enable'] = true
        prometheus['monitor_kubernetes'] = false

        # 安全配置
        nginx['ssl_certificate'] = "/etc/gitlab/ssl/gitlab.crt"
        nginx['ssl_certificate_key'] = "/etc/gitlab/ssl/gitlab.key"
        nginx['ssl_protocols'] = "TLSv1.2 TLSv1.3"
        nginx['ssl_ciphers'] = "ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256"
        nginx['ssl_prefer_server_ciphers'] = "on"
        nginx['ssl_session_cache'] = "builtin:1000 shared:SSL:10m"
        nginx['ssl_session_timeout'] = "5m"

    ports:
      - "80:80"
      - "443:443"
      - "22:22"

    volumes:
      - ./config:/etc/gitlab
      - ./data:/var/opt/gitlab
      - ./logs:/var/log/gitlab
      - ./backups:/var/opt/gitlab/backups
      - /etc/localtime:/etc/localtime:ro

    shm_size: '256m'

    healthcheck:
      test: ["CMD", "/opt/gitlab/bin/gitlab-healthcheck", "--fail", "--max-time", "10"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 200s

networks:
  default:
    name: gitlab-network
EOF
```

### 步骤 3：生产环境配置

```bash
cat > docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  gitlab:
    image: gitlab/gitlab-ee:15.11.0-ee.0
    container_name: gitlab-ee
    restart: unless-stopped
    hostname: gitlab.company.com

    environment:
      TZ: 'Asia/Shanghai'
      GITLAB_OMNIBUS_CONFIG: |
        # 基础配置
        external_url 'https://gitlab.company.com'
        gitlab_rails['time_zone'] = 'Asia/Shanghai'

        # 数据库配置（外部 PostgreSQL）
        postgresql['enable'] = false
        gitlab_rails['db_adapter'] = 'postgresql'
        gitlab_rails['db_encoding'] = 'unicode'
        gitlab_rails['db_host'] = 'postgres.company.com'
        gitlab_rails['db_port'] = 5432
        gitlab_rails['db_database'] = 'gitlab_production'
        gitlab_rails['db_username'] = 'gitlab'
        gitlab_rails['db_password'] = 'secure_password'

        # Redis 配置（外部 Redis）
        redis['enable'] = false
        gitlab_rails['redis_host'] = 'redis.company.com'
        gitlab_rails['redis_port'] = 6379
        gitlab_rails['redis_password'] = 'redis_password'

        # 对象存储配置
        gitlab_rails['object_store']['enabled'] = true
        gitlab_rails['object_store']['proxy_download'] = true
        gitlab_rails['object_store']['connection'] = {
          'provider' => 'AWS',
          'region' => 'us-east-1',
          'aws_access_key_id' => 'your-access-key',
          'aws_secret_access_key' => 'your-secret-key',
          'endpoint' => 'https://s3.company.com'
        }

        # LDAP 集成
        gitlab_rails['ldap_enabled'] = true
        gitlab_rails['ldap_servers'] = {
          'main' => {
            'label' => 'Company LDAP',
            'host' => 'ldap.company.com',
            'port' => 389,
            'uid' => 'uid',
            'bind_dn' => 'cn=gitlab,ou=services,dc=company,dc=com',
            'password' => 'ldap_password',
            'encryption' => 'plain',
            'verify_certificates' => true,
            'active_directory' => false,
            'allow_username_or_email_login' => true,
            'base' => 'dc=company,dc=com',
            'user_filter' => '(objectClass=inetOrgPerson)',
            'attributes' => {
              'username' => ['uid'],
              'email' => ['mail'],
              'name' => 'cn',
              'first_name' => 'givenName',
              'last_name' => 'sn'
            }
          }
        }

        # 高可用配置
        gitlab_rails['auto_migrate'] = false

        # 性能优化
        unicorn['worker_processes'] = 4
        sidekiq['max_concurrency'] = 25
        postgresql['shared_preload_libraries'] = 'pg_stat_statements'

        # 监控配置
        prometheus['enable'] = true
        prometheus['listen_address'] = '0.0.0.0:9090'
        node_exporter['enable'] = true
        redis_exporter['enable'] = true
        postgres_exporter['enable'] = true

    ports:
      - "80:80"
      - "443:443"
      - "22:22"
      - "9090:9090"

    volumes:
      - ./config:/etc/gitlab
      - ./data:/var/opt/gitlab
      - ./logs:/var/log/gitlab
      - ./backups:/var/opt/gitlab/backups
      - /etc/localtime:/etc/localtime:ro

    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 4G
          cpus: '2'

    healthcheck:
      test: ["CMD", "/opt/gitlab/bin/gitlab-healthcheck", "--fail", "--max-time", "10"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 300s

networks:
  default:
    name: gitlab-network
    driver: bridge
EOF
```

### 步骤 4：SSL 证书配置

```bash
# 创建 SSL 证书目录
mkdir -p config/ssl

# 生成自签名证书（测试环境）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout config/ssl/gitlab.key \
  -out config/ssl/gitlab.crt \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=Company/CN=gitlab.your-domain.com"

# 或使用 Let's Encrypt（生产环境）
# certbot certonly --standalone -d gitlab.your-domain.com
# cp /etc/letsencrypt/live/gitlab.your-domain.com/fullchain.pem config/ssl/gitlab.crt
# cp /etc/letsencrypt/live/gitlab.your-domain.com/privkey.pem config/ssl/gitlab.key

# 设置证书权限
chmod 600 config/ssl/gitlab.key
chmod 644 config/ssl/gitlab.crt
```

### 步骤 5：启动服务

```bash
# 开发环境启动
docker-compose up -d

# 生产环境启动
docker-compose -f docker-compose.prod.yml up -d

# 查看启动日志
docker-compose logs -f gitlab

# 等待服务完全启动（通常需要 5-10 分钟）
docker-compose exec gitlab gitlab-ctl status
```

![image-20210409163819513](https://cdn.treesir.pub/img/image-20210409163819513.png)

### 步骤 6：初始化配置

```bash
# 获取初始 root 密码
docker-compose exec gitlab cat /etc/gitlab/initial_root_password

# 或者重置 root 密码
docker-compose exec gitlab gitlab-rake "gitlab:password:reset[root]"
```

### 步骤 7：验证部署

访问 GitLab Web 界面：`https://gitlab.your-domain.com`

![image-20210409164819832](https://cdn.treesir.pub/img/image-20210409164819832.png)

**健康检查脚本**：

```bash
cat > check-gitlab-health.sh << 'EOF'
#!/bin/bash

GITLAB_URL="https://gitlab.your-domain.com"
CONTAINER_NAME="gitlab-ce"

echo "=== GitLab 健康检查 ==="

# 检查容器状态
echo "1. 检查容器状态"
docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 检查 GitLab 服务状态
echo -e "\n2. 检查 GitLab 服务状态"
docker exec $CONTAINER_NAME gitlab-ctl status

# 检查 Web 界面响应
echo -e "\n3. 检查 Web 界面响应"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" $GITLAB_URL)
if [ "$HTTP_CODE" = "200" ]; then
    echo "✓ Web 界面响应正常"
else
    echo "✗ Web 界面响应异常 (HTTP $HTTP_CODE)"
fi

# 检查 API 响应
echo -e "\n4. 检查 API 响应"
API_RESPONSE=$(curl -s "$GITLAB_URL/api/v4/version")
if [ $? -eq 0 ]; then
    echo "✓ API 响应正常: $API_RESPONSE"
else
    echo "✗ API 响应异常"
fi

# 检查资源使用
echo -e "\n5. 检查资源使用"
docker stats $CONTAINER_NAME --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo -e "\n=== 健康检查完成 ==="
EOF

chmod +x check-gitlab-health.sh
./check-gitlab-health.sh
```





## 方案二：Kubernetes 部署

### Helm Chart 部署

```bash
# 添加 GitLab Helm 仓库
helm repo add gitlab https://charts.gitlab.io/
helm repo update

# 创建命名空间
kubectl create namespace gitlab

# 创建配置文件
cat > gitlab-values.yaml << 'EOF'
global:
  hosts:
    domain: your-domain.com
    https: true
  ingress:
    configureCertmanager: true
    class: nginx

certmanager:
  install: true

nginx-ingress:
  enabled: true

prometheus:
  install: true

grafana:
  enabled: true

gitlab-runner:
  install: true
  runners:
    privileged: true
EOF

# 部署 GitLab
helm upgrade --install gitlab gitlab/gitlab \
  -f gitlab-values.yaml \
  -n gitlab \
  --timeout 600s
```

# 反向代理配置

## Nginx 反向代理

### 安装 Nginx

```bash
# CentOS/RHEL
yum install -y nginx

# Ubuntu/Debian
apt-get update && apt-get install -y nginx
```

### 基础代理配置

```bash
cat > /etc/nginx/conf.d/gitlab.conf << 'EOF'
# GitLab upstream
upstream gitlab {
    server 127.0.0.1:8080 fail_timeout=0;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name gitlab.your-domain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS 配置
server {
    listen 443 ssl http2;
    server_name gitlab.your-domain.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/gitlab.crt;
    ssl_certificate_key /etc/nginx/ssl/gitlab.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志配置
    access_log /var/log/nginx/gitlab.access.log;
    error_log /var/log/nginx/gitlab.error.log;

    # 客户端上传限制
    client_max_body_size 1G;

    # 代理配置
    location / {
        proxy_pass http://gitlab;
        proxy_redirect off;

        # 代理头设置
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Ssl on;

        # 超时设置
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Git HTTP(S) 优化
    location ~ ^/[\w\.-]+/[\w\.-]+/repository/archive {
        proxy_pass http://gitlab;
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # Container Registry 代理
    location /v2/ {
        proxy_pass http://gitlab;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
        proxy_request_buffering off;
    }
}
EOF
```

### 高可用 Nginx 配置

```bash
cat > /etc/nginx/conf.d/gitlab-ha.conf << 'EOF'
# GitLab 高可用 upstream
upstream gitlab_ha {
    least_conn;
    server gitlab-node1.your-domain.com:80 max_fails=3 fail_timeout=30s;
    server gitlab-node2.your-domain.com:80 max_fails=3 fail_timeout=30s;
    server gitlab-node3.your-domain.com:80 max_fails=3 fail_timeout=30s;

    # 健康检查
    keepalive 32;
}

server {
    listen 443 ssl http2;
    server_name gitlab.your-domain.com;

    # SSL 配置（同上）

    location / {
        proxy_pass http://gitlab_ha;

        # 会话保持
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $http_host;

        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }
}
EOF
```

### 启动和验证

```bash
# 测试配置
nginx -t

# 重新加载配置
nginx -s reload

# 设置开机自启
systemctl enable nginx
systemctl start nginx

# 查看状态
systemctl status nginx
```

## Traefik 反向代理

### Docker Compose 集成

```bash
cat > docker-compose.traefik.yml << 'EOF'
version: '3.8'

services:
  traefik:
    image: traefik:v2.9
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik
      - ./acme.json:/acme.json
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.your-domain.com`)"
      - "traefik.http.routers.dashboard.tls=true"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"

  gitlab:
    image: gitlab/gitlab-ce:15.11.0-ce.0
    container_name: gitlab-ce
    restart: unless-stopped
    volumes:
      - ./config:/etc/gitlab
      - ./data:/var/opt/gitlab
      - ./logs:/var/log/gitlab
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.gitlab.rule=Host(`gitlab.your-domain.com`)"
      - "traefik.http.routers.gitlab.tls=true"
      - "traefik.http.routers.gitlab.tls.certresolver=letsencrypt"
      - "traefik.http.services.gitlab.loadbalancer.server.port=80"
      - "traefik.http.middlewares.gitlab-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
      - "traefik.http.routers.gitlab.middlewares=gitlab-headers"

networks:
  default:
    name: traefik-network
EOF
```

![image-20210409165223276](https://cdn.treesir.pub/img/image-20210409165223276.png)

## 功能验证

### 基础功能测试

```bash
# 创建测试脚本
cat > test-gitlab-functions.sh << 'EOF'
#!/bin/bash

GITLAB_URL="https://gitlab.your-domain.com"
API_TOKEN="your-api-token"

echo "=== GitLab 功能测试 ==="

# 测试 API 访问
echo "1. 测试 API 访问"
curl -s -H "PRIVATE-TOKEN: $API_TOKEN" "$GITLAB_URL/api/v4/version" | jq .

# 测试项目创建
echo -e "\n2. 测试项目创建"
PROJECT_DATA='{
  "name": "test-project",
  "description": "Test project for GitLab functionality",
  "visibility": "private"
}'

PROJECT_RESPONSE=$(curl -s -X POST \
  -H "PRIVATE-TOKEN: $API_TOKEN" \
  -H "Content-Type: application/json" \
  -d "$PROJECT_DATA" \
  "$GITLAB_URL/api/v4/projects")

PROJECT_ID=$(echo $PROJECT_RESPONSE | jq -r .id)
echo "项目创建成功，ID: $PROJECT_ID"

# 测试文件上传
echo -e "\n3. 测试文件上传"
echo "# Test README" > README.md
FILE_RESPONSE=$(curl -s -X POST \
  -H "PRIVATE-TOKEN: $API_TOKEN" \
  -F "branch=main" \
  -F "commit_message=Add README" \
  -F "file=@README.md" \
  "$GITLAB_URL/api/v4/projects/$PROJECT_ID/repository/files/README.md")

echo "文件上传结果: $(echo $FILE_RESPONSE | jq -r .file_path)"

# 清理测试项目
echo -e "\n4. 清理测试项目"
curl -s -X DELETE \
  -H "PRIVATE-TOKEN: $API_TOKEN" \
  "$GITLAB_URL/api/v4/projects/$PROJECT_ID"

echo "测试完成"
EOF

chmod +x test-gitlab-functions.sh
```

![image-20210409172159652](https://cdn.treesir.pub/img/image-20210409172159652.png)

![image-20210409172547967](https://cdn.treesir.pub/img/image-20210409172547967.png)

![image-20210409172607523](https://cdn.treesir.pub/img/image-20210409172607523.png)





# GitLab 企业级配置优化

## 系统级配置优化

### 性能调优配置

```bash
# 编辑 GitLab 配置文件
docker-compose exec gitlab vi /etc/gitlab/gitlab.rb

# 或者通过环境变量配置
cat >> docker-compose.yml << 'EOF'
    environment:
      GITLAB_OMNIBUS_CONFIG: |
        # 性能优化配置
        unicorn['worker_processes'] = 4
        unicorn['worker_timeout'] = 60

        # Sidekiq 优化
        sidekiq['max_concurrency'] = 25
        sidekiq['min_concurrency'] = 5

        # PostgreSQL 优化
        postgresql['shared_buffers'] = "256MB"
        postgresql['effective_cache_size'] = "1GB"
        postgresql['work_mem'] = "16MB"
        postgresql['maintenance_work_mem'] = "64MB"
        postgresql['checkpoint_completion_target'] = 0.9
        postgresql['wal_buffers'] = "16MB"
        postgresql['default_statistics_target'] = 100

        # Redis 优化
        redis['maxmemory'] = "256mb"
        redis['maxmemory_policy'] = "allkeys-lru"

        # Gitaly 优化
        gitaly['ruby_max_rss'] = 300000000
        gitaly['concurrency'] = [
          {
            'rpc' => "/gitaly.SmartHTTPService/PostReceivePack",
            'max_per_repo' => 3
          },
          {
            'rpc' => "/gitaly.SSHService/SSHUploadPack",
            'max_per_repo' => 3
          }
        ]
EOF
```

### 安全配置强化

```bash
cat > security-config.rb << 'EOF'
# 安全配置
gitlab_rails['rack_attack_git_basic_auth'] = {
  'enabled' => true,
  'ip_whitelist' => ["127.0.0.1", "::1"],
  'maxretry' => 10,
  'findtime' => 60,
  'bantime' => 3600
}

# 密码策略
gitlab_rails['password_authentication_enabled_for_web'] = true
gitlab_rails['password_authentication_enabled_for_git'] = true
gitlab_rails['password_minimum_length'] = 8
gitlab_rails['password_require_uppercase'] = true
gitlab_rails['password_require_lowercase'] = true
gitlab_rails['password_require_number'] = true
gitlab_rails['password_require_symbol'] = false

# 会话安全
gitlab_rails['session_expire_delay'] = 10080
gitlab_rails['session_store_secure'] = true

# API 限制
gitlab_rails['rate_limit_requests_per_period'] = 1000
gitlab_rails['rate_limit_period'] = 60

# 文件上传限制
gitlab_rails['max_attachment_size'] = 100
nginx['client_max_body_size'] = '100m'

# 禁用不必要的功能
gitlab_rails['usage_ping_enabled'] = false
gitlab_rails['sentry_enabled'] = false
EOF
```

## 功能模块配置

### 关闭 Auto DevOps

```bash
# 通过 API 全局关闭 Auto DevOps
curl -X PUT \
  -H "PRIVATE-TOKEN: your-api-token" \
  -H "Content-Type: application/json" \
  -d '{"auto_devops_enabled": false}' \
  "https://gitlab.your-domain.com/api/v4/application/settings"
```

![image-20210412150002697](https://cdn.treesir.pub/img/image-20210412150002697.png)

### Webhook 配置优化

```bash
# 启用 Webhook 并配置安全设置
cat >> gitlab.rb << 'EOF'
gitlab_rails['webhook_timeout'] = 10
gitlab_rails['webhook_max_redirects'] = 3
gitlab_rails['webhook_allowed_local_network_ranges'] = ['10.0.0.0/8', '**********/12', '***********/16']
EOF
```

![image-20210412150343435](https://cdn.treesir.pub/img/image-20210412150343435.png)

### 接口请求限制配置

```bash
# 配置 Rate Limiting
cat >> gitlab.rb << 'EOF'
gitlab_rails['rate_limit_by_ip'] = {
  'enabled' => true,
  'requests_per_period' => 1000,
  'period' => 60,
  'ban_duration' => 3600
}

gitlab_rails['rate_limit_by_user'] = {
  'enabled' => true,
  'requests_per_period' => 1000,
  'period' => 60,
  'ban_duration' => 3600
}
EOF
```

![image-20210412150549008](https://cdn.treesir.pub/img/image-20210412150549008.png)

### 监控配置

```bash
# 启用内置监控
cat >> gitlab.rb << 'EOF'
# Prometheus 配置
prometheus['enable'] = true
prometheus['listen_address'] = '0.0.0.0:9090'
prometheus['scrape_interval'] = 15
prometheus['scrape_timeout'] = 15

# Node Exporter
node_exporter['enable'] = true
node_exporter['listen_address'] = '0.0.0.0:9100'

# Redis Exporter
redis_exporter['enable'] = true
redis_exporter['listen_address'] = '0.0.0.0:9121'

# PostgreSQL Exporter
postgres_exporter['enable'] = true
postgres_exporter['listen_address'] = '0.0.0.0:9187'

# Grafana 配置
grafana['enable'] = true
grafana['admin_password'] = 'secure_password'
grafana['disable_login_form'] = false
grafana['allow_user_sign_up'] = false
EOF
```

![image-20210412150825760](https://cdn.treesir.pub/img/image-20210412150825760.png)

![image-20210412151147827](https://cdn.treesir.pub/img/image-20210412151147827.png)

![image-20210412151401340](https://cdn.treesir.pub/img/image-20210412151401340.png)

### 本地化配置

```bash
# 时区和语言配置
cat >> gitlab.rb << 'EOF'
gitlab_rails['time_zone'] = 'Asia/Shanghai'
gitlab_rails['default_locale'] = 'zh_CN'
gitlab_rails['default_theme'] = 2  # 深色主题
EOF
```

![image-20210412151612643](https://cdn.treesir.pub/img/image-20210412151612643.png)

### SSH 访问控制

```bash
# SSH 配置优化
cat >> gitlab.rb << 'EOF'
gitlab_rails['gitlab_shell_ssh_port'] = 22
gitlab_rails['gitlab_shell_git_timeout'] = 800

# 禁用 SSH 访问（如果只使用 HTTPS）
gitlab_rails['gitlab_shell_ssh_port'] = nil
EOF
```

![image-20210412152449920](https://cdn.treesir.pub/img/image-20210412152449920.png)

### 用户注册控制

```bash
# 用户注册和权限配置
cat >> gitlab.rb << 'EOF'
gitlab_rails['signup_enabled'] = false
gitlab_rails['signin_enabled'] = true
gitlab_rails['require_two_factor_authentication'] = false
gitlab_rails['two_factor_grace_period'] = 48

# 新用户默认权限
gitlab_rails['default_can_create_group'] = false
gitlab_rails['default_can_create_team'] = false
gitlab_rails['default_projects_limit'] = 0
gitlab_rails['default_project_visibility'] = 'private'
gitlab_rails['default_snippet_visibility'] = 'private'
gitlab_rails['default_group_visibility'] = 'private'
EOF
```

![image-20210412152950209](https://cdn.treesir.pub/img/image-20210412152950209.png)

![image-20210412153256775](https://cdn.treesir.pub/img/image-20210412153256775.png)

## 高级功能配置

### Container Registry 配置

```bash
# 启用 Container Registry
cat >> gitlab.rb << 'EOF'
registry_external_url 'https://registry.your-domain.com'
registry['enable'] = true
registry['registry_http_addr'] = "0.0.0.0:5000"
registry['debug_addr'] = "localhost:5001"
registry['log_level'] = "info"
registry['rootcertbundle'] = "/etc/gitlab/ssl/ca.crt"

# 存储配置
registry['storage'] = {
  's3' => {
    'accesskey' => 'your-access-key',
    'secretkey' => 'your-secret-key',
    'bucket' => 'gitlab-registry',
    'region' => 'us-east-1',
    'regionendpoint' => 'https://s3.your-domain.com'
  }
}

# 垃圾回收
registry['gc_cron_jobs'] = [
  {
    'cron' => '0 2 * * *',
    'job' => 'registry garbage-collect /etc/docker/registry/config.yml'
  }
]
EOF
```

### CI/CD Runner 集成

```bash
# GitLab Runner 配置
cat > register-runner.sh << 'EOF'
#!/bin/bash

GITLAB_URL="https://gitlab.your-domain.com"
REGISTRATION_TOKEN="your-registration-token"

# 注册 Docker Runner
gitlab-runner register \
  --non-interactive \
  --url "$GITLAB_URL" \
  --registration-token "$REGISTRATION_TOKEN" \
  --executor "docker" \
  --docker-image alpine:latest \
  --description "docker-runner" \
  --tag-list "docker,linux" \
  --run-untagged="true" \
  --locked="false" \
  --access-level="not_protected"

# 注册 Kubernetes Runner
gitlab-runner register \
  --non-interactive \
  --url "$GITLAB_URL" \
  --registration-token "$REGISTRATION_TOKEN" \
  --executor "kubernetes" \
  --kubernetes-host "https://kubernetes.your-domain.com" \
  --kubernetes-namespace "gitlab-runner" \
  --description "k8s-runner" \
  --tag-list "kubernetes,k8s"
EOF

chmod +x register-runner.sh
```

### 备份配置

```bash
# 自动备份配置
cat >> gitlab.rb << 'EOF'
gitlab_rails['backup_keep_time'] = 604800  # 7 days
gitlab_rails['backup_path'] = "/var/opt/gitlab/backups"
gitlab_rails['backup_archive_permissions'] = 0644
gitlab_rails['backup_pg_schema'] = 'public'

# 备份加密
gitlab_rails['backup_encryption'] = 'aes256'
gitlab_rails['backup_encryption_key'] = 'your-encryption-key'

# 远程备份存储
gitlab_rails['backup_upload_connection'] = {
  'provider' => 'AWS',
  'region' => 'us-east-1',
  'aws_access_key_id' => 'your-access-key',
  'aws_secret_access_key' => 'your-secret-key'
}
gitlab_rails['backup_upload_remote_directory'] = 'gitlab-backups'
gitlab_rails['backup_multipart_chunk_size'] = 104857600
EOF

# 创建备份脚本
cat > backup-gitlab.sh << 'EOF'
#!/bin/bash

echo "开始 GitLab 备份..."
docker-compose exec gitlab gitlab-backup create

echo "备份配置文件..."
tar -czf /data/gitlab/backups/gitlab-config-$(date +%Y%m%d).tar.gz \
  /data/gitlab/config/gitlab.rb \
  /data/gitlab/config/gitlab-secrets.json

echo "备份完成"
EOF

chmod +x backup-gitlab.sh

# 设置定时备份
echo "0 2 * * * /data/gitlab/backup-gitlab.sh" | crontab -

# 数据恢复与灾难恢复

## 数据备份策略

### 完整备份脚本

```bash
cat > gitlab-full-backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/data/gitlab/backups"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)

echo "=== GitLab 完整备份脚本 ==="
echo "开始时间: $(date)"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 1. 创建 GitLab 数据备份
echo "1. 创建 GitLab 数据备份..."
docker-compose exec -T gitlab gitlab-backup create BACKUP=$DATE

# 2. 备份配置文件
echo "2. 备份配置文件..."
tar -czf "$BACKUP_DIR/gitlab-config-$DATE.tar.gz" \
  config/gitlab.rb \
  config/gitlab-secrets.json \
  config/ssl/

# 3. 备份 Docker Compose 文件
echo "3. 备份 Docker Compose 文件..."
cp docker-compose.yml "$BACKUP_DIR/docker-compose-$DATE.yml"

# 4. 创建备份清单
echo "4. 创建备份清单..."
cat > "$BACKUP_DIR/backup-manifest-$DATE.txt" << MANIFEST
GitLab 备份清单
================
备份时间: $(date)
GitLab 版本: $(docker-compose exec -T gitlab cat /opt/gitlab/version-manifest.txt | head -1)
数据备份: ${DATE}_gitlab_backup.tar
配置备份: gitlab-config-$DATE.tar.gz
Compose 文件: docker-compose-$DATE.yml

备份文件列表:
$(ls -la $BACKUP_DIR/*$DATE*)
MANIFEST

# 5. 验证备份完整性
echo "5. 验证备份完整性..."
if [ -f "$BACKUP_DIR/${DATE}_gitlab_backup.tar" ]; then
    echo "✓ 数据备份文件存在"
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/${DATE}_gitlab_backup.tar" | cut -f1)
    echo "  备份大小: $BACKUP_SIZE"
else
    echo "✗ 数据备份文件不存在"
    exit 1
fi

# 6. 清理过期备份
echo "6. 清理过期备份..."
find $BACKUP_DIR -name "*gitlab_backup.tar" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "gitlab-config-*.tar.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "docker-compose-*.yml" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "backup-manifest-*.txt" -mtime +$RETENTION_DAYS -delete

# 7. 发送备份报告
echo "7. 发送备份报告..."
if command -v mail >/dev/null 2>&1; then
    cat "$BACKUP_DIR/backup-manifest-$DATE.txt" | \
    mail -s "GitLab 备份报告 - $(date +%Y-%m-%d)" <EMAIL>
fi

echo "=== 备份完成 ==="
echo "备份文件: $BACKUP_DIR/${DATE}_gitlab_backup.tar"
echo "配置文件: $BACKUP_DIR/gitlab-config-$DATE.tar.gz"
EOF

chmod +x gitlab-full-backup.sh
```

### 增量备份脚本

```bash
cat > gitlab-incremental-backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/data/gitlab/backups"
INCREMENTAL_DIR="$BACKUP_DIR/incremental"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $INCREMENTAL_DIR

echo "=== GitLab 增量备份 ==="

# 查找最近的完整备份
LAST_FULL_BACKUP=$(ls -t $BACKUP_DIR/*_gitlab_backup.tar 2>/dev/null | head -1)

if [ -z "$LAST_FULL_BACKUP" ]; then
    echo "未找到完整备份，执行完整备份..."
    ./gitlab-full-backup.sh
    exit 0
fi

echo "基于完整备份: $(basename $LAST_FULL_BACKUP)"

# 创建增量备份（仅备份变更的仓库）
docker-compose exec -T gitlab gitlab-backup create BACKUP=incremental_$DATE STRATEGY=incremental

# 备份最近修改的配置
find config/ -newer "$LAST_FULL_BACKUP" -type f | \
    tar -czf "$INCREMENTAL_DIR/config-incremental-$DATE.tar.gz" -T -

echo "增量备份完成: incremental_${DATE}_gitlab_backup.tar"
EOF

chmod +x gitlab-incremental-backup.sh
```

## 数据恢复

### 完整恢复脚本

```bash
cat > gitlab-restore.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/data/gitlab/backups"

echo "=== GitLab 数据恢复脚本 ==="

# 列出可用备份
echo "可用的备份文件:"
ls -la $BACKUP_DIR/*_gitlab_backup.tar | nl

# 选择备份文件
read -p "请输入要恢复的备份文件编号: " BACKUP_NUM
BACKUP_FILE=$(ls $BACKUP_DIR/*_gitlab_backup.tar | sed -n "${BACKUP_NUM}p")

if [ ! -f "$BACKUP_FILE" ]; then
    echo "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

BACKUP_TIMESTAMP=$(basename $BACKUP_FILE | sed 's/_gitlab_backup.tar//')
echo "选择的备份: $BACKUP_TIMESTAMP"

read -p "确认恢复？这将覆盖现有数据 (yes/no): " CONFIRM
if [ "$CONFIRM" != "yes" ]; then
    echo "恢复已取消"
    exit 0
fi

# 停止 GitLab 服务
echo "停止 GitLab 服务..."
docker-compose stop gitlab

# 恢复数据
echo "恢复 GitLab 数据..."
docker-compose run --rm gitlab gitlab-backup restore BACKUP=$BACKUP_TIMESTAMP

# 恢复配置文件
if [ -f "$BACKUP_DIR/gitlab-config-$BACKUP_TIMESTAMP.tar.gz" ]; then
    echo "恢复配置文件..."
    tar -xzf "$BACKUP_DIR/gitlab-config-$BACKUP_TIMESTAMP.tar.gz"
fi

# 重新配置 GitLab
echo "重新配置 GitLab..."
docker-compose run --rm gitlab gitlab-ctl reconfigure

# 启动服务
echo "启动 GitLab 服务..."
docker-compose up -d gitlab

# 等待服务启动
echo "等待服务启动..."
sleep 60

# 验证恢复
echo "验证恢复结果..."
docker-compose exec gitlab gitlab-rake gitlab:check

echo "=== 恢复完成 ==="
EOF

chmod +x gitlab-restore.sh
```

# 监控与告警

## 系统监控

### Prometheus 监控配置

```bash
cat > prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "gitlab-rules.yml"

scrape_configs:
  - job_name: 'gitlab'
    static_configs:
      - targets: ['gitlab:9090']
    metrics_path: '/-/metrics'

  - job_name: 'gitlab-workhorse'
    static_configs:
      - targets: ['gitlab:9229']

  - job_name: 'gitlab-sidekiq'
    static_configs:
      - targets: ['gitlab:8082']

  - job_name: 'gitlab-gitaly'
    static_configs:
      - targets: ['gitlab:9236']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF
```

### 告警规则配置

```bash
cat > gitlab-rules.yml << 'EOF'
groups:
  - name: gitlab
    rules:
      - alert: GitLabDown
        expr: up{job="gitlab"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "GitLab is down"
          description: "GitLab has been down for more than 5 minutes."

      - alert: GitLabHighCPU
        expr: rate(process_cpu_seconds_total{job="gitlab"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "GitLab high CPU usage"
          description: "GitLab CPU usage is above 80% for more than 10 minutes."

      - alert: GitLabHighMemory
        expr: process_resident_memory_bytes{job="gitlab"} / 1024 / 1024 / 1024 > 4
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "GitLab high memory usage"
          description: "GitLab memory usage is above 4GB for more than 10 minutes."

      - alert: GitLabDiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/var/opt/gitlab"} / node_filesystem_size_bytes{mountpoint="/var/opt/gitlab"}) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "GitLab disk space low"
          description: "GitLab disk space is below 10%."

      - alert: GitLabBackupFailed
        expr: increase(gitlab_backup_last_backup_time[1d]) == 0
        for: 1d
        labels:
          severity: warning
        annotations:
          summary: "GitLab backup failed"
          description: "GitLab backup has not run successfully in the last 24 hours."
EOF
```

### 监控脚本

```bash
cat > gitlab-monitor.sh << 'EOF'
#!/bin/bash

GITLAB_URL="https://gitlab.your-domain.com"
CONTAINER_NAME="gitlab-ce"
LOG_FILE="/var/log/gitlab-monitor.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 检查容器状态
check_container() {
    if docker ps --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -q $CONTAINER_NAME; then
        log "✓ GitLab 容器运行正常"
        return 0
    else
        log "✗ GitLab 容器未运行"
        return 1
    fi
}

# 检查 Web 服务
check_web_service() {
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" $GITLAB_URL)
    if [ "$response_code" = "200" ]; then
        log "✓ GitLab Web 服务响应正常"
        return 0
    else
        log "✗ GitLab Web 服务响应异常 (HTTP $response_code)"
        return 1
    fi
}

# 检查 API 服务
check_api_service() {
    local api_response=$(curl -s "$GITLAB_URL/api/v4/version")
    if echo "$api_response" | grep -q "version"; then
        log "✓ GitLab API 服务正常"
        return 0
    else
        log "✗ GitLab API 服务异常"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    local usage=$(df /data/gitlab | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -lt 80 ]; then
        log "✓ 磁盘空间充足 (${usage}%)"
        return 0
    elif [ $usage -lt 90 ]; then
        log "⚠ 磁盘空间不足 (${usage}%)"
        return 1
    else
        log "✗ 磁盘空间严重不足 (${usage}%)"
        return 2
    fi
}

# 检查内存使用
check_memory_usage() {
    local memory_usage=$(docker stats $CONTAINER_NAME --no-stream --format "{{.MemPerc}}" | sed 's/%//')
    if (( $(echo "$memory_usage < 80" | bc -l) )); then
        log "✓ 内存使用正常 (${memory_usage}%)"
        return 0
    else
        log "⚠ 内存使用较高 (${memory_usage}%)"
        return 1
    fi
}

# 检查备份状态
check_backup_status() {
    local last_backup=$(ls -t /data/gitlab/backups/*_gitlab_backup.tar 2>/dev/null | head -1)
    if [ -n "$last_backup" ]; then
        local backup_age=$(( ($(date +%s) - $(stat -c %Y "$last_backup")) / 86400 ))
        if [ $backup_age -le 1 ]; then
            log "✓ 备份状态正常 (最近备份: $backup_age 天前)"
            return 0
        else
            log "⚠ 备份过期 (最近备份: $backup_age 天前)"
            return 1
        fi
    else
        log "✗ 未找到备份文件"
        return 1
    fi
}

# 发送告警
send_alert() {
    local message="$1"
    local severity="$2"

    # 邮件告警
    if command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "GitLab 监控告警 [$severity]" <EMAIL>
    fi

    # 钉钉告警
    if [ -n "$DINGTALK_WEBHOOK" ]; then
        curl -X POST "$DINGTALK_WEBHOOK" \
            -H 'Content-Type: application/json' \
            -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"GitLab 告警: $message\"}}"
    fi

    # Slack 告警
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST "$SLACK_WEBHOOK" \
            -H 'Content-Type: application/json' \
            -d "{\"text\": \"GitLab Alert: $message\"}"
    fi
}

# 主监控函数
main() {
    log "=== GitLab 监控检查开始 ==="

    local issues=0
    local critical_issues=0

    check_container || ((issues++))
    check_web_service || ((issues++))
    check_api_service || ((issues++))

    check_disk_space
    local disk_status=$?
    if [ $disk_status -eq 2 ]; then
        ((critical_issues++))
        send_alert "GitLab 磁盘空间严重不足" "CRITICAL"
    elif [ $disk_status -eq 1 ]; then
        ((issues++))
    fi

    check_memory_usage || ((issues++))
    check_backup_status || ((issues++))

    if [ $critical_issues -gt 0 ]; then
        log "✗ 发现 $critical_issues 个严重问题"
        send_alert "GitLab 监控发现 $critical_issues 个严重问题" "CRITICAL"
    elif [ $issues -gt 0 ]; then
        log "⚠ 发现 $issues 个问题"
        send_alert "GitLab 监控发现 $issues 个问题" "WARNING"
    else
        log "✓ 所有检查通过"
    fi

    log "=== GitLab 监控检查完成 ==="
}

main "$@"
EOF

chmod +x gitlab-monitor.sh

# 设置定时监控
echo "*/5 * * * * /data/gitlab/gitlab-monitor.sh" | crontab -
```

# 故障排除

## 常见问题诊断

### 启动问题

```bash
cat > diagnose-gitlab.sh << 'EOF'
#!/bin/bash

echo "=== GitLab 故障诊断 ==="

# 检查容器状态
echo "1. 检查容器状态"
docker ps -a --filter "name=gitlab"

# 检查容器日志
echo -e "\n2. 检查容器日志"
docker logs --tail 50 gitlab-ce

# 检查 GitLab 服务状态
echo -e "\n3. 检查 GitLab 服务状态"
docker exec gitlab-ce gitlab-ctl status

# 检查配置文件
echo -e "\n4. 检查配置文件"
docker exec gitlab-ce gitlab-ctl show-config

# 检查磁盘空间
echo -e "\n5. 检查磁盘空间"
df -h /data/gitlab

# 检查内存使用
echo -e "\n6. 检查内存使用"
free -h

# 检查网络连接
echo -e "\n7. 检查网络连接"
netstat -tlnp | grep -E "(80|443|22)"

# 检查 GitLab 健康状态
echo -e "\n8. 检查 GitLab 健康状态"
docker exec gitlab-ce gitlab-rake gitlab:check

echo -e "\n=== 诊断完成 ==="
EOF

chmod +x diagnose-gitlab.sh
```

### 性能问题

```bash
cat > gitlab-performance-analysis.sh << 'EOF'
#!/bin/bash

CONTAINER_NAME="gitlab-ce"

echo "=== GitLab 性能分析 ==="

# 容器资源使用
echo "1. 容器资源使用"
docker stats $CONTAINER_NAME --no-stream

# GitLab 进程状态
echo -e "\n2. GitLab 进程状态"
docker exec $CONTAINER_NAME ps aux | head -20

# 数据库连接
echo -e "\n3. 数据库连接"
docker exec $CONTAINER_NAME gitlab-rails runner "puts ActiveRecord::Base.connection_pool.stat"

# Redis 状态
echo -e "\n4. Redis 状态"
docker exec $CONTAINER_NAME redis-cli info memory

# Sidekiq 队列状态
echo -e "\n5. Sidekiq 队列状态"
docker exec $CONTAINER_NAME gitlab-rails runner "puts Sidekiq::Queue.new.size"

# 磁盘 I/O
echo -e "\n6. 磁盘 I/O"
iostat -x 1 3

echo -e "\n=== 性能分析完成 ==="
EOF

chmod +x gitlab-performance-analysis.sh
```

# 总结

## 部署优势

通过本指南，您可以成功部署一个企业级的 GitLab 平台，具有以下优势：

### 技术优势
- **多种部署方式**：支持 Docker Compose 和 Kubernetes 部署
- **高可用性**：支持负载均衡和分布式架构
- **安全可靠**：集成 LDAP 认证、SSL 加密、访问控制
- **可扩展性**：支持水平扩展和模块化配置
- **监控完善**：内置 Prometheus 监控和 Grafana 可视化

### 运维优势
- **自动化备份**：完整的备份恢复策略
- **性能优化**：数据库、缓存、应用层全面优化
- **故障排除**：完善的诊断和恢复机制
- **企业集成**：支持 LDAP、SAML、OAuth 等认证方式

## 最佳实践

### 生产环境建议
1. **资源规划**：根据用户数量和项目规模合理规划资源
2. **安全配置**：启用 HTTPS、配置防火墙、定期安全审计
3. **备份策略**：建立完善的备份和灾难恢复计划
4. **监控告警**：部署全面的监控和告警体系
5. **性能调优**：根据使用情况持续优化配置参数

### 扩展建议
- **高可用部署**：配置多节点集群和负载均衡
- **对象存储集成**：使用 S3 兼容存储降低成本
- **CI/CD 优化**：配置专用 Runner 和缓存策略
- **安全扫描集成**：启用 SAST、DAST、依赖扫描

## 持续改进

GitLab 作为 DevOps 平台的核心，需要持续优化和改进：

- **定期更新**：保持 GitLab 版本的及时更新
- **性能监控**：持续监控系统性能和用户体验
- **安全审计**：定期进行安全检查和漏洞扫描
- **用户培训**：提供 Git 和 GitLab 使用培训

通过本指南的配置和最佳实践，您可以构建一个稳定、高效、安全的企业级 GitLab 平台，为团队的代码管理和 DevOps 实践提供强有力的支撑。
```



# 总结

Gitlab 为企业中 常见的 git 代码管理软件，是在实施 `Devops` 过程中的利剑，功能强大，并内置了 ci/cd、repository、监控等功能。在对 Gitlab 进行配置优化时，我们需要注意在配置时还 存在着`全局生效配置` 和 `项目局部生效配置`，如在全局中配置了未见生效，可以去对应的项目组中查看此项目对应配置也做了相应的更改否。

