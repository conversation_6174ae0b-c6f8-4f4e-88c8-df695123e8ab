---
title: "JIRA & Confluence 完整安装配置指南"
date: 2021-02-21T11:02:00+08:00
draft: false
tags: [ "jira", "install", "confluence"]
tags_weight: 80
categories: ["devops"]
categories_weight: 80
keywords:
- jira
- 安装
- 配置
- confluence
- 8.x
- 项目管理
description: "详细介绍 JIRA 8.x 和 Confluence 的完整安装配置流程，包含数据库配置、环境准备等步骤"
---

# 系统环境要求

## 环境信息

| 组件 | 版本 | 说明 |
|------|------|------|
| 操作系统 | CentOS 7+ | 推荐使用 CentOS 7 或更高版本 |
| JIRA 版本 | 8.13.4 | 企业级项目管理工具 |
| 数据库 | MySQL 5.7 | 存储 JIRA 数据 |
| Java 环境 | OpenJDK 1.8 | JIRA 运行环境 |

> 💡 **提示**：可以访问 [JIRA 官方下载页面](https://www.atlassian.com/zh/software/jira/download) 查看最新版本信息

## 安装前准备

### 什么是 JIRA？

JIRA 是 Atlassian 公司开发的项目管理和问题跟踪工具，广泛用于：
- **项目管理**：敏捷开发、任务分配、进度跟踪
- **问题跟踪**：Bug 管理、需求管理
- **团队协作**：工作流程管理、报告生成

# 环境准备

## 步骤 1：安装 Java 环境

JIRA 需要 Java 8 或更高版本的支持。

```bash
# 安装 OpenJDK 1.8
yum install -y java-1.8.0-openjdk

# 验证安装结果
java -version
```

**预期输出：**
```bash
openjdk version "1.8.0_282"
OpenJDK Runtime Environment (build 1.8.0_282-b08)
OpenJDK 64-Bit Server VM (build 25.282-b08, mixed mode)
```

## 步骤 2：安装 MySQL 5.7 数据库

### 为什么需要数据库？

JIRA 需要数据库来存储：
- 项目数据和配置信息
- 用户账户和权限设置
- 工作流和自定义字段
- 历史记录和报告数据

### 配置 MySQL 软件源

```bash
# 下载 MySQL 官方 YUM 源
wget https://dev.mysql.com/get/mysql57-community-release-el7-11.noarch.rpm

# 安装 YUM 源
yum localinstall mysql57-community-release-el7-11.noarch.rpm -y

# 验证软件源是否配置成功
yum repolist enabled | grep "mysql.*-community.*"
```

**预期输出：**
```bash
mysql-connectors-community/x86_64 MySQL Connectors Community                 185
mysql-tools-community/x86_64      MySQL Tools Community                      123
mysql57-community/x86_64          MySQL 5.7 Community Server                 484
```

### 安装 MySQL 服务

```bash
# 安装 MySQL 5.7 社区版
yum install -y mysql-community-server
```

### 启动 MySQL 服务

```bash
# 启动 MySQL 服务
systemctl start mysqld

# 检查服务状态
systemctl status mysqld

# 设置开机自启动
systemctl enable mysqld
```

![image-20210221123804575](https://cdn.treesir.pub/img/image-20210221123804575.png)

### 初始化数据库配置

#### 获取初始密码

MySQL 5.7 安装后会自动生成一个临时的 root 密码：

```bash
# 查看临时密码
grep 'temporary password' /var/log/mysqld.log
```

**示例输出：**
```bash
2021-02-21T04:37:14.500441Z 1 [Note] A temporary password is generated for root@localhost: p!o2lkYqNXQu
```

> 📝 **记录密码**：请记住获取到的临时密码，例如：`p!o2lkYqNXQu`

#### 修改 root 密码

```bash
# 使用临时密码登录
mysql -uroot -p

# 修改 root 密码（请替换为您的强密码）
mysql> ALTER USER 'root'@'localhost' IDENTIFIED BY 'YourStrongPassword123!';

# 刷新权限
mysql> flush privileges;
```

![image-20210221124608385](https://cdn.treesir.pub/img/image-20210221124608385.png)

### 创建 JIRA 数据库

#### 创建专用数据库

> 📚 **参考文档**：[JIRA 官方 MySQL 配置指南](https://confluence.atlassian.com/adminjiraserver0813/connecting-jira-applications-to-mysql-5-7-1027137456.html)

```bash
# 创建 JIRA 专用数据库
mysql> CREATE DATABASE `jiradb` CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
```

#### 创建专用数据库用户

为了提高安全性，我们为 JIRA 创建一个专用的数据库用户：

```bash
# 临时降低密码复杂度要求（仅用于演示）
mysql> set global validate_password_policy=0;

# 创建 JIRA 专用用户并授权
mysql> GRANT SELECT,INSERT,UPDATE,DELETE,CREATE,DROP,REFERENCES,ALTER,INDEX
       on jiradb.* TO jira_user@'%' IDENTIFIED BY '12345678';

# 刷新权限
mysql> flush privileges;
```

> ⚠️ **安全提示**：在生产环境中，请使用更强的密码，并考虑限制用户的访问来源

![image-20210221125446095](https://cdn.treesir.pub/img/image-20210221125446095.png)

### 优化 MySQL 配置

为了确保 JIRA 的最佳性能，需要调整 MySQL 配置：

```bash
# 编辑 MySQL 配置文件
vi /etc/my.cnf

# 在 [mysqld] 部分添加以下配置
[mysqld]
# 设置默认存储引擎
default-storage-engine=INNODB

# 字符集配置
character_set_server=utf8mb4

# InnoDB 配置优化
innodb_default_row_format=DYNAMIC
innodb_large_prefix=ON
innodb_file_format=Barracuda
innodb_log_file_size=2G

# SQL 模式配置
sql_mode=NO_AUTO_VALUE_ON_ZERO

# 重启 MySQL 服务使配置生效
service mysqld restart
```

**配置说明：**
- `utf8mb4`：支持完整的 Unicode 字符集
- `DYNAMIC`：支持大型索引前缀
- `2G`：增大日志文件大小以提升性能

![image-20210221134045070](https://cdn.treesir.pub/img/image-20210221134045070.png)

# JIRA 软件安装

## 步骤 3：下载并安装 JIRA

### 下载 JIRA 安装包

完成数据库配置后，开始安装 JIRA 软件：

```bash
# 下载 JIRA 8.13.4 安装包
wget https://cdn.treesir.pub/application/jira/atlassian-jira-software-8.13.4-x64.bin

# 添加执行权限
chmod a+x atlassian-jira-software-8.13.4-x64.bin

# 运行安装程序
./atlassian-jira-software-8.13.4-x64.bin
```

### 安装过程截图

![image-20210221131107289](https://cdn.treesir.pub/img/image-20210221131107289.png)

![image-20210221131128890](https://cdn.treesir.pub/img/image-20210221131128890.png)

> 💡 **安装提示**：安装过程中选择默认选项即可，JIRA 会安装到 `/opt/atlassian/jira/` 目录

## 步骤 4：配置数据库连接

### 安装 MySQL 驱动

JIRA 需要 MySQL JDBC 驱动来连接数据库：

```bash
# 下载 MySQL 连接器
wget https://cdn.treesir.pub/application/jira/mysql-connector-java-5.1.49.tar.gz

# 解压驱动包
tar xf mysql-connector-java-5.1.49.tar.gz

# 复制驱动到 JIRA 库目录
cp mysql-connector-java-5.1.49/mysql-connector-java-5.1.49-bin.jar \
   /opt/atlassian/jira/atlassian-jira/WEB-INF/lib/
```

> 📝 **说明**：MySQL 驱动允许 JIRA 与 MySQL 数据库进行通信

## 步骤 5：配置许可证管理

### 下载许可证工具

```bash
# 下载许可证管理工具
wget https://cdn.treesir.pub/application/jira/atlassian-agent-v1.2.3.zip

# 安装解压工具（如果未安装）
yum install unzip -y

# 解压工具包
unzip atlassian-agent-v1.2.3.zip

# 创建工具目录
mkdir -p /opt/atlassian/jira/jar

# 复制许可证工具
cp atlassian-agent-v1.2.3/atlassian-agent.jar /opt/atlassian/jira/jar/
```

> ⚠️ **重要说明**：此工具仅用于学习和测试环境，生产环境请购买正版许可证



## 修改配置文件

> 在 `JAVA_OPTS` 一行中添加 `-javaagent:/opt/atlassian/jira/jar/atlassian-agent.jar`

```bash
cd /opt/atlassian/jira/bin

vim setenv.sh  # 编辑配置文件
JAVA_OPTS="-javaagent:/opt/atlassian/jira/jar/atlassian-agent.jar -Xms${JVM_MINIMUM_MEMORY} -Xmx${JVM_MAXIMUM_MEMORY} ${JVM_CODE_CACHE_ARGS} ${JAVA_OPTS} ${JVM_REQUIRED_ARGS} ${DISABLE_NOTIFICATIONS} ${JVM_SUPPORT_RECOMMENDED_ARGS} ${JVM_EXTRA_ARGS} ${JIRA_HOME_MINUSD} ${START_JIRA_JAVA_OPTS}"

service jira stop \
&& service jira start # 重启服务
```



## dashboard 初始化配置

![image-20210221132221317](https://cdn.treesir.pub/img/image-20210221132221317.png)

![image-20210221134106904](https://cdn.treesir.pub/img/image-20210221134106904.png)

![image-20210221134349334](https://cdn.treesir.pub/img/image-20210221134349334.png)

> **记住 server id**

## 使用工具 获取 `license`

> `复制服务器 id 放至 -s 之后`

```bash
cd /opt/atlassian/jira/jar/

/opt/atlassian/jira/jre/bin/java -jar atlassian-agent.jar -p jira -m <EMAIL> -n my_name -o https://zhile.io -s BEVE-RLKR-E429-3JWB

====================================================
=======        Atlassian Crack Agent         =======
=======           https://zhile.io           =======
=======          QQ Group: 30347511          =======
====================================================

Your license code(Don't copy this line!!!): 

AAABfw0ODAoPeJx9klFrwjAUhd/7Kwp7TrVVnAqBzTaDblWHVfc40nrVjDYtN6mb+/WLths6RQiEh
JyT75577+JK2k+Q2J5ru51htzdsd20/ntte23OtDQLIbVGWgE4kUpAK5vsSJjwH6k/HYzbzw8fI8
hG4FoUMuAZ6EJK2R4z8hiQAlaIoDyq6kJnIhYaVndUCO9nbW61LNWy1vrciA0cU1pgLqUFymQL7K
gXum9/6A9K+N8v6EMh/KdlK1NaTKByHcxZYkypPAKfrhQJUlPzB3fAqsVhVqXYOB6KKtf7kCM6F0
Y23PNViB1RjBWdZnt43NS+N24HYs9iOZ9UxT7rmmQJrihsuhaqvLnLxC6mNHzP5ZJRz/pAkiZMWe
c11nfb0/xv4seaoARuMJrAwoFEYxGxCIrfndvr9XqfXdV33LP9rLY8Bd4BGPmJLRmbRy4ywrjcgn
ee30bVJu+zha4Xpliv4P2enYjBDgiUK1ZRnQOkV2Ca1I2O+f5dm/wFMhgkNMCwCFDyreObHK4tWu
NBWoOQpy/gWRwsUAhR9tkejACVL+tc1RzzSopum6rxwfg==X02im
```

![image-20210221134657881](https://cdn.treesir.pub/img/image-20210221134657881.png)

# confluence 安装

> confluence 安装过程与 jira 安装大致相同 (数据驱动+破解补丁)  如在一台机器上可使用同一个破解补丁，只要保证端口不冲突即可。下面展示与 jira 安装的 `不同项`。

## confluence 安装与 jira 安装不同项展示

- 安装包 下载

  ```bash
  wget https://cdn.treesir.pub/application/jira/atlassian-confluence-7.4.1-x64.bin
  ```

- 数据库隔离级别

  ```bash
  # 编辑 my.cnf 添加配置，修改事务提交级别
  vi /etc/my.cnf
  [mysqld]
  transaction-isolation=READ-COMMITTED
  
  
  mysql>  SET GLOBAL tx_isolation='READ-COMMITTED';
  mysql>  select @@tx_isolation;
  mysql>  CREATE DATABASE `confluence_db` CHARACTER SET utf8 COLLATE utf8_bin;
  
  cp  mysql-connector-java-5.1.49/mysql-connector-java-5.1.49-bin.jar /opt/atlassian/confluence/confluence/WEB-INF/lib
  ```

- setenv.sh 文件

  ```shell
  # javaagent 为破解补丁地址
  export JAVA_OPTS="-javaagent:/opt/atlassian/jira/jar/atlassian-agent.jar ${JAVA_OPTS}"  #最上边添加这行配置
  ```

- 使用破解补丁获取 license

  ```shell
  /opt/atlassian/confluence/jre/bin/java -jar /opt/atlassian/jira/jar/atlassian-agent.jar -p conf -m <EMAIL> -n my_name -o https://zhile.io -s B6RI-Q8HH-ZDR0-FDZ1 # -s 后接你服务器id 
  ```


# 问题记录

- 启动权限问题解决

  ```shell
  chown confluence:confluence -R /opt/atlassian/confluence/
  sudo chown -R confluence:confluence /opt/atlassian/confluence
  sudo chown -R confluence:confluence /home/<USER>
  sudo chmod -R u=rwx,g=rx,o=rx /opt/atlassian/confluence
  sudo chmod -R u=rwx,g=rx,o=rx /home/<USER>
  ```

- 如安装过程中，需要卸载安装。

  ```shell
  /opt/atlassian/confluence/uninstall 
  rm -rf /var/atlassian/application-data/confluence
  
  /opt/atlassian/jira/uninstall 
  rm -rf /var/atlassian/application-data/jira
  ```


# 配置 nginx 代理
> Jira 默认监听在主机中的 `8080` 端口上, 为了 `方便记忆` 通常会修改成域名且 `不带后面的端口号` 的形式进行访问。不带端口号的话，这样就是要使用 `80` or `443`端口号了，显然我们这里是内网，不需要什么安全性，只需要使用 80 端口就行。如果让 Jira 监听在 80 端口上的话，显的有些浪费，为了不保证浪费此时我们就可以使用另外一个工具 `nginx` 配置虚拟机主机来实现反代, 这样就可以同时将 80 端口提供给 `多个服务`进行使用了。

- Jira nginx 配置 [**参考文档**](https://confluence.atlassian.com/jirakb/configure-jira-server-to-run-behind-a-nginx-reverse-proxy-426115340.html)

**修改 Jira 配置文件**

```bash
vi /opt/atlassian/jira/conf/server.xml

        <Connector port="8080" relaxedPathChars="[]|" relaxedQueryChars="[]|{}^&#x5c;&#x60;&quot;&lt;&gt;"
                   maxThreads="150" minSpareThreads="25" connectionTimeout="20000" enableLookups="false"
                   maxHttpHeaderSize="8192" protocol="HTTP/1.1" useBodyEncodingForURI="true" redirectPort="8443"
                   acceptCount="100" disableUploadTimeout="true" bindOnInit="false" proxyName="jira.treesir.pub" proxyPort="80"/>
```



> 添加 `proxyName` & `proxyPort` 参数后, 重启服务。
>
> ```bash
> service jira stop \
> && service jira start
> ```

**Nginx 添加虚拟机主机**

```bash
cat /etc/nginx/conf.d/jira.conf 
server {
    listen 80;
    server_name jira.treesir.pub;
    location / {
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
        proxy_pass http://*************:8080;
        client_max_body_size 0;
    }
}

nginx -t 
  nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
  nginx: configuration file /etc/nginx/nginx.conf test is successful     # 检查配置文件
```

> `*************:8080` 为你 Jira 的地址，不要配置 `127.0.0.1:8080` 这样好像会报错。
