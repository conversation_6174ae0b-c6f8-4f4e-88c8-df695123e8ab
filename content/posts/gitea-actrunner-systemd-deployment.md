---
title: "Gitea Actions ActRunner 基于 Systemd 部署安装"
date: 2023-07-25T22:06:35+08:00
draft: false
tags: [ "DevOps", "Linux" ]
tags_weight: 80
categories: ["SRE"]
categories_weight: 80
keywords:
- ActRunner
- Gitea
- pipeline
- Systemd
- Deployment
- Actions
description: "Gitea ActRunner Actions Systemd Deployment. Gitea Actions ActRunner 基于 Systemd 部署安装"
---

# 说明

> Gitea是一个开源的自助式Git服务，用于托管和管理Git仓库。它是一个轻量级且易于安装和使用的解决方案，类似于GitHub或GitLab，可以在私有服务器上搭建自己的Git仓库服务。Gitea提供了一系列功能，使团队或个人能够方便地进行版本控制和协作开发，包括：
>
> 1. 仓库管理：可以创建、克隆、推送和拉取Git仓库，管理分支和标签，查看提交历史和代码差异等操作。
>
> 2. 用户和权限管理：可以创建用户账号，管理用户的访问权限，并为不同的用户或团队分配不同的角色和权限，以控制仓库的访问和操作。
>
> 3. 问题追踪：提供了一个问题追踪系统，用户可以创建和管理问题、缺陷或需求，并与仓库的代码和提交相关联，方便团队进行协作和解决问题。
>
> 4. Pull请求：支持Pull请求（合并请求），允许用户在不直接修改主分支的情况下，通过提出Pull请求来建议将自己的代码合并到项目中。
>
> 5. 代码审查：Gitea提供了代码审查功能，可以让团队成员对提交的代码进行审查、讨论和评论，以提高代码质量和团队协作。
>
> 6. 集成与插件：Gitea支持与其他工具和服务的集成，如邮件通知、CI/CD工具和第三方插件等。
>
> Gitea 从 `1.19` 开始支持 Actions，使用前提需要像 GitlabRunner 一样部署一个独立的 Runner 来管理 Job，名叫 `ActRunner`，具体可参考文档:
>
> - https://docs.gitea.com/next/usage/actions/overview

# Act_Runner 部署安装

1. Gitea `app.ini` 配置文件添加如下内容，开启 `Actions` 功能 

   ```bash
   [repo.actions]
   ENABLED = true
   ```

2. act_runner 编译安装

   ```bash
   $ git clone https://gitea.com/gitea/act_runner.git
   $ cd act_runner
   $ make build
   
   mv act_runner /usr/local/bin
   ```

3. acr_runner 注册

   > - 注册 Token，到 Gitea 网页后台进行获取
   > - 执行后会在所在目录生成 `.runner 文件`，记住这个执行时所在目录，后面要用。

   ```bash
   cd /root/act_runner
   
   act_runner register --instance https://git.treesir.pub \
     --token xxxx --no-interactive
   ```

4. 生成配置文件

   ```bash
   mkdir -p /etc/act_runner/
   
   act_runner generate-config > /etc/act_runner/config.yaml # 生成配置文件
   
   cat /etc/act_runner/config.yaml|egrep -v '^#|^$|  #'
   ```
   
   **更改过后的配置文件展示**
   
   ![image-20230725222059594](https://cdn.treesir.pub/img/image-20230725222059594.png)
   
5. 使用 systemctl 管理

   > ⚠️
   >
   > - `PATH` 环境变量需要根据机器实际情况更改一下，可以执行 `echo $PATH` 来获取自身的 PATH 变量信息作为替换。
   >
   > - `WorkingDirectory` 需要更改为在执行 `上面第三步` 时所在目录，我这里为 `/root/act_runner`
   > - 由于简化部署原因，这样所使用的用户为 `root`, 如对安全性有要求的同学，可以进行更改，主要更改后的用户需要拥有Docker 权限。

   ```bash
   cat > /etc/systemd/system/gitea-runner.service << EOF
   Description=Gitea Actions runner
   Documentation=https://gitea.com/gitea/act_runner
   After=docker.service
   
   [Service]
   Environment=PATH=/opt/jdk/bin/:/usr/local/mysql/bin:/usr/local/openresty/nginx/sbin:/root/.autojump/bin:/root/.cargo/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/go/bin:/opt/go/bin:/opt/jdk/jre/bin:/opt/maven/bin:/root/.cargo/bin
   ExecStart=/usr/local/bin/act_runner daemon -c /etc/act_runner/config.yaml
   ExecReload=/bin/kill -s HUP
   WorkingDirectory=/root/act_runner
   NotifyAccess=all
   User=root
   Group=root
   LimitNOFILE=65536
   
   [Install]
   WantedBy=default.target
   EOF
   ```

   ![image-20230725222947766](https://cdn.treesir.pub/img/image-20230725222947766.png)

5. 设置开机自启动

   ```bash
   systemctl enable gitea-runner --now
   ```

#  总结

> Gitea Actions 使用风格与 Github 的类似，简单做了一下测试，么有什么问题。更加深入的使用后续再做研究。

![image-20230725223457841](https://cdn.treesir.pub/img/image-20230725223457841.png)

![image-20230725223614024](https://cdn.treesir.pub/img/image-20230725223614024.png)
