---
title: "OpenVpn 的安装，并配置关联 openLdap 认证"
date: 2021-04-22T16:19:56+08:00
draft: false
tags: [ "openvpn","install"]
tags_weight: 20
categories: ["linux", "network"]
categories_weight: 20
keywords:
- openvpn
- linux
- centos
- vpn
- openldap
description: "openvpn 在 centos 中的配置，并配置开启了 openldap 进行用户认证"
---



# 环境说明

- `docker-compose`：1.23.2
- 操作系统: `armbian (斐讯n1)`

# 部署软件

## openldap 部署

> 省略 openldap 的部署配置 请参考之前整理的 [`文档`](https://www.treesir.pub/post/docker-deploy-ldap/)

## openvpn 安装

> 这里 openvpn 一键安装使用 github 中的一键脚本
>
> [脚本地址](https://github.com/Nyr/openvpn-install)

```bash
curl -o openvpn-install.sh  https://raw.githubusercontent.com/Nyr/openvpn-install/master/openvpn-install.sh


chmod a+x openvpn-install.sh

./openvpn-install.sh

Welcome to this OpenVPN road warrior installer!

Which IPv4 address should be used?
     1) ***********12
     2) **********
     3) **********
     4) *********
     5) *********
IPv4 address [1]: 1

This server is behind NAT. What is the public IPv4 address or hostname?

Public IPv4 address / hostname [**************]: 
Which protocol should OpenVPN use?
   1) UDP (recommended)
   2) TCP
Protocol [1]: 1

What port should OpenVPN listen to?
Port [1194]: 1194

Select a DNS server for the clients:
   1) Current system resolvers
   2) Google
   3) *******
   4) OpenDNS
   5) Quad9
   6) AdGuard
DNS server [1]: 1

Enter a name for the first client:
Name [client]: ***********

OpenVPN installation is ready to begin.
```

![image-20210513212630543](https://cdn.treesir.pub/img/image-20210513212630543.png)

> 等待程序的执行完毕 设置程序为开启自启动
>
> ```bash
> <NAME_EMAIL>
> ```

**开放端口**

> 如开启和使用了防火墙，请注意将 对应端口开放

```bash
iptables -A INPUT -p udp --sport 1194 -j ACCEPT
iptables -A OUTPUT -p udp --dport 1194 -j ACCEPT
```

# 配置关联openldap

## 安装依赖包

```bash
 apt install openvpn-auth-ldap  # 如果是 centos 将前面命令更改为 yum 即可
```

> 安装完毕后，查看使用有如下 `依赖库文件`
>
> ```bash
> ls -lh  /usr/lib/openvpn/openvpn-auth-ldap.so 
> -rw-r--r-- 1 <USER> <GROUP> 126K Nov  7  2017 /usr/lib/openvpn/openvpn-auth-ldap.so
> ```
>
> centos 下的文件路径为 `/usr/lib64/openvpn/plugin/lib/openvpn-auth-ldap.so`



## 配置文件增加配置

```bash
vi /etc/openvpn/server/server.conf  # 在最后添加如下配置

plugin /usr/lib/openvpn/openvpn-auth-ldap.so  "/etc/openvpn/server/ldap.conf"
client-cert-not-required
username-as-common-name
management 0.0.0.0 5555
```

![image-20210513213649178](https://cdn.treesir.pub/img/image-20210513213649178.png)

**增加 openldap配置文件**

```bash
cat > /etc/openvpn/server/ldap.conf << EOF `# 注意修改下面对应的配置`
<LDAP>
    URL ldap://***********:389
    BindDN  cn=admin,dc=treesir,dc=pub
    Password 123456
    Timeout 15
    TLSEnable no
    FollowReferrals no
</LDAP>
<Authorization>
    BaseDN "ou=users,dc=treesir,dc=pub"
    SearchFilter "(uid=%u)"
    RequireGroup false
</Authorization>
EOF
```

> 示例中 ldap 目录使用结构 [参考文档](https://www.treesir.pub/post/docker-deploy-ldap/#openldap-%E7%9B%AE%E5%BD%95%E7%BB%93%E6%9E%84%E7%9A%84%E5%88%9D%E5%A7%8B%E5%8C%96)



**更改完成后，我们重启服务使其生效**

```bash
service openvpn-server@server restart \
&& service openvpn-server@server status
```


# 测试验证

**配置 nps 内网穿透的使用**

> 将配置文件里的 `remote` 地址更改为公网地址，没有公网地址的话，可以使用第三方内网穿透工具实现, 下面示例将使用自建的 `nps` 作为演示

![image-20210513215613617](https://cdn.treesir.pub/img/image-20210513215613617.png)

> 注意这个映射地址外面设置成了 `21174` 那么在对应的配置文件中也要相应更改成这个，还有就是协议需要 和安装时选择的对应上，我这里使用的是 `udp` 协议。



**下载安装 vpn 时的客户端配置文件**

```bash
sz -y /root/192_168_8_1.ovpn  ## 默认在 用户的家目录中
```



**编辑下载的配置文件**

```bash
remote nps.treesir.pub 21194 # 将此地址更改为 公网地址，没有公网地址的话，可以使用第三方内网穿透工具实现

auth-user-pass # 添加用户密码认证
auth-nocache # 认证不缓存
setenv opt block-outside-dns # Prevent Windows 10 DNS leak
```

![image-20210513223000606](https://cdn.treesir.pub/img/image-20210513223000606.png)

![image-20210513223013339](https://cdn.treesir.pub/img/image-20210513223013339.png)

![image-20210513223023620](https://cdn.treesir.pub/img/image-20210513223023620.png)



**手机端测试**

> 将配置文件上传至手机端进行测试

![image-20210513223326718](https://cdn.treesir.pub/img/image-20210513223326718.png)

> 可以看到手机端使用流量，测试也是正常可以访问到内网的。

# 部署 openldap dashboard

```bash
docker run --name openvpn-monitor \
  -e OPENVPNMONITOR_DEFAULT_DATETIMEFORMAT="%%d/%%m/%%Y" \
  -e OPENVPNMONITOR_DEFAULT_LATITUDE=-37 \
  -e OPENVPNMONITOR_DEFAULT_LOGO=logo.jpg \
  -e OPENVPNMONITOR_DEFAULT_LONGITUDE=144 \
  -e OPENVPNMONITOR_DEFAULT_MAPS=True \
  -e OPENVPNMONITOR_DEFAULT_SITE=Test \
  -e OPENVPNMONITOR_SITES_0_ALIAS=UDP \
  -e OPENVPNMONITOR_SITES_0_HOST=***********12 \
  -e OPENVPNMONITOR_SITES_0_NAME=UDP \
  -e OPENVPNMONITOR_SITES_0_PORT=5555 \
  -e OPENVPNMONITOR_SITES_1_ALIAS=TCP \
  -e OPENVPNMONITOR_SITES_1_HOST=***********12 \
  -e OPENVPNMONITOR_SITES_1_NAME=TCP \
  -e OPENVPNMONITOR_SITES_1_PORT=5555 \
  --restart=always \
  -p 8210:80 \
  -d ruimarinho/openvpn-monitor
```

![image-20210513224453580](https://cdn.treesir.pub/img/image-20210513224453580.png)

> 可以看到，dashboard 中的 `remote ip` 是 ***********13，且map中无对应显示地址 ( map 更具 remote ip 显示 )， 这是因为我使用了内网穿透技术的缘故，企业中建议是绑定到对应的公网ip中，并使用 `DNAT` 作为端口映射，这样dashboard 中的 map 显示将呈现正常。

# 客户端软件下载

[openvpn-install-2.4.8-I602-Win10](https://cdn.treesir.pub/tools/openvpn/openvpn-install-2.4.8-I602-Win10.exe)

[openvpn-install-2.4.6-I602-Win7](https://cdn.treesir.pub/tools/openvpn/openvpn-install-2.4.6-I602-Win7.exe)

[openvpn-android-install](https://cdn.treesir.pub/tools/openvpn/openvpn-android-install.apk)
