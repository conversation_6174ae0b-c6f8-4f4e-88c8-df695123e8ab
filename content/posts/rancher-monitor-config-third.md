---
title: "Rancher 开启监控后的，阈值告警配置说明 (三)"
date: 2021-05-19T8:41:26+08:00
draft: false
tags: [ "rancher", "prometheus", "operator", "k8s", "kubekey", "exporter"]
tags_weight: 80
categories: ["devops","k8s","prometheus","alertmanage"]
categories_weight: 80
keywords:
- rancher
- prometheus
- operator
- devops
- k8s
- kubernetes
- alertmanage
description: "使用 rancher prometheus operator 开启监控后的监控阈值告警配置"
---

# 环境说明

> 此文档为 rancher `monitor`  使用系列的 `第三篇` ，主要介绍与 `dingtalk` 关联配置告警通知、prometheus 告警阈值的配置使用



# 配置安装 dingtalk webhook

> [webhook github 地址](https://github.com/timonwong/prometheus-webhook-dingtalk)



## 安装 `kustomize`

```bash
curl -s "https://raw.githubusercontent.com/\
kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash

mv kustomize /usr/local/bin/

kustomize version
{Version:kustomize/v4.1.2 GitCommit:a5914abad89e0b18129eaf1acc784f9fe7d21439 BuildDate:2021-04-15T20:38:06Z GoOs:linux GoArch:amd64}
```



## 使用 kustomize 部署 deployment

- clone 代码

  ```bash
  yum install -y git  # 安装 git
  
  git clone https://github.com/timonwong/prometheus-webhook-dingtalk.git
  
  cd prometheus-webhook-dingtalk/contrib/k8s/
  ```

- 创建 dingtalk  自定义机器人

  > 安全设置这里我们选择 `加签` 处理

  ![image-20210519133905640](https://cdn.treesir.pub/img/image-20210519133905640.png)

  ![image-20210519133947031](https://cdn.treesir.pub/img/image-20210519133947031.png)

  



> 复制机器人 `webhook` 信息填入至配置文件中

```bash
vi config/config.yaml 
```

![image-20210519134222203](https://cdn.treesir.pub/img/image-20210519134222203.png)

- 部署 `yaml` 配置文件

  ```bash
  sed -i "s#monitoring#prometheus#g" kustomization.yaml # 修改 配置文件中指定部署的 命名空间
  
  kustomize build|kubectl apply -f -  # 执行部署
  configmap/alertmanager-webhook-dingtalk-f5hkbg7g25 created
  service/alertmanager-webhook-dingtalk created
  deployment.apps/alertmanager-webhook-dingtalk created
  ```

  ![image-20210519134932057](https://cdn.treesir.pub/img/image-20210519134932057.png)

- 测试是否可以正常发送通知

  ```bash
  kubectl get po alertmanager-webhook-dingtalk-5c7b48fd9d-bcpxb  -n prometheus  -o wide # 获取 pod ip
  
  curl 'http://************:8060/dingtalk/webhook1/send' \
  -H 'Content-Type: application/json' \
  -d '{"Status": "testing"}'
  ```

  ![image-20210519135949715](https://cdn.treesir.pub/img/image-20210519135949715.png)

  ![](https://cdn.treesir.pub/img/image-20210519140006822.png)

> 正常将信息发送至 机器人，而且还做了转 `大写` 处理，这是因为我们默认使用的 [`template`](https://github.com/timonwong/prometheus-webhook-dingtalk/blob/master/template/default.tmpl) 里面做了操作导致。

![image-20210519140244159](https://cdn.treesir.pub/img/image-20210519140244159.png)

# Rancher alertmanage 配置

> alertmanage 的安装非常简单 只需要在 rancher 对应的 dashboard 中设置开启通知， 就会自动帮我去创建相应的 `alertmanage` clustert。



## 关联前面部署的 dingtalk `webhook`

**点击通知**

![image-20210519141008974](https://cdn.treesir.pub/img/image-20210519141008974.png)

**添加通知**

![image-20210519141030325](https://cdn.treesir.pub/img/image-20210519141030325.png)

> 选择添加 `webhook` 类型通知，名称随意设置，webhook 的地址，我们这里配置使用 k8s `内部域名`，测试时无法解析此域名属于正常现象，因为所部署的 `rancher` 并不在当前k8s中，只要保证后面的 `alertmanage`可以正常访问即可，确认后点击添加。 

![image-20210519141310991](https://cdn.treesir.pub/img/image-20210519141310991.png)



## 手动测试，告警是否有生效



**点击告警**

![image-20210519141856845](https://cdn.treesir.pub/img/image-20210519141856845.png)

**选择一组告警组 关联刚才添加的通知**

![image-20210519141933493](https://cdn.treesir.pub/img/image-20210519141933493.png)

`拉到最下面` 设置为我们上面添加的 告警

![image-20210519141953871](https://cdn.treesir.pub/img/image-20210519141953871.png)

可以看到此时我们关联告警后，`operator` 后端为我们自动去创建了 `alertmanger `

![image-20210519142151126](https://cdn.treesir.pub/img/image-20210519142151126.png)

**手动将阈值降低，触发告警**

> 这里我们已 cpu 一分钟平均负载情况，作为示例，更改为 使用到 `5% `就触发告警，持续时间更改为 1s

![image-20210519142449382](https://cdn.treesir.pub/img/image-20210519142449382.png)

![image-20210519142639541](https://cdn.treesir.pub/img/image-20210519142639541.png)

等待一会后，已触发告警的发送了

![image-20210519142918636](https://cdn.treesir.pub/img/image-20210519142918636.png)

> 这里告警出来后，alertmanger 这边还需要一段时间进行处理，趁这个时间，我们把 alertmanager 的 service 类型更改为前面 prometheus 一样的 `NodePort` 类型。 
>
> ```bash
> kubectl edit svc access-alertmanager -n cattle-prometheus
> ...
>     app: alertmanager
>     chart: alertmanager-0.0.1
>     release: cluster-alerting
>   sessionAffinity: None
>   type: NodePort
> ...
> ```

**我们使用 对应的 NodePort 进行访问 alertmanger 的 dashboard**

![image-20210519143437776](https://cdn.treesir.pub/img/image-20210519143437776.png)

> 可以看到 `alertmanger` 中也已有对应的告警通知，再次查看一下 dingtalk 这边也正常将通知出来了。

![image-20210519143557394](https://cdn.treesir.pub/img/image-20210519143557394.png)

**当我们把 阈值还原时，恢复通知也正常发送了出来**

![image-20210519143800615](https://cdn.treesir.pub/img/image-20210519143800615.png)



# 通知优化及阈值告警配置

## 阈值告警配置

> `alert rule` 请参考 [此链接](https://awesome-prometheus-alerts.grep.to/rules.html)。

**在对应的告警组或新键的告警组中，参考上面`链接`中的 rule 配置添加即可 **

![image-20210519153020606](https://cdn.treesir.pub/img/image-20210519153020606.png)

![image-20210519153249261](https://cdn.treesir.pub/img/image-20210519153249261.png)

> 这里需要注意一下的就是，使用 `表达式` 的这种方法相对更加灵活一些，还有就是需要注意一下 `rancher` 中也有逻辑判断符的，这里需要注意一下，不要 `重复` 添加了。

## 通知优化

- 图标链接更改优化

  ![image-*****************](https://cdn.treesir.pub/img/image-*****************.png)

![image-*****************](https://cdn.treesir.pub/img/image-*****************.png)

> 可以看到点击图标跳转的这个地址不对，对于故障的定位排除，点击就能跳转到正确的页面还是非常有之必要的。

- 修改 `prometheus` 资源对象进行添加 `externalUrl` 配置即可

  ```bash
  kubectl edit prometheus -n cattle-prometheus
  ...
    securityContext:
      fsGroup: 2000
      runAsNonRoot: true
      runAsUser: 1000
    serviceAccountName: cluster-monitoring
    externalUrl: http://192.168.8.30:32209
  ...
  ```

  ![image-*****************](https://cdn.treesir.pub/img/image-*****************.png)

![image-*****************](https://cdn.treesir.pub/img/image-*****************.png)

> 检查对应的 `yaml` 可以看到 args 中 已添加了 `--web.external-url` 配置了,  `alertmanager` 的配置添加也是一样的，同样添加 `externalUrl`  字段即可。

# To Do
.....
