---
title: "TOC 粘性定位测试"
date: 2025-08-02T19:20:00+08:00
draft: false
description: "测试TOC目录的粘性定位功能是否正常工作"
categories: ["测试"]
tags: ["toc", "测试", "粘性定位"]
author: "测试"
showTableOfContents: true
---

这是一个用于测试TOC（目录）粘性定位功能的文章。文章内容足够长，可以测试滚动时TOC是否保持在屏幕右侧。

## 第一章：介绍

这是第一章的内容。我们需要足够的内容来测试TOC的粘性定位功能。

### 1.1 子章节

这是第一章的第一个子章节。

### 1.2 另一个子章节

这是第一章的第二个子章节。

## 第二章：详细内容

这是第二章的开始。我们继续添加更多内容来测试滚动效果。

### 2.1 技术细节

这里是一些技术细节的内容。

#### 2.1.1 深入技术

这是更深层次的技术内容。

#### 2.1.2 更多技术

继续添加技术内容。

### 2.2 实践应用

这里是实践应用的内容。

## 第三章：高级主题

这是第三章的内容，讨论高级主题。

### 3.1 高级配置

高级配置的相关内容。

### 3.2 性能优化

性能优化的相关内容。

#### 3.2.1 缓存策略

缓存策略的详细说明。

#### 3.2.2 压缩技术

压缩技术的详细说明。

## 第四章：故障排除

这是第四章，专门讨论故障排除。

### 4.1 常见问题

常见问题的解决方案。

### 4.2 调试技巧

调试技巧和方法。

## 第五章：最佳实践

这是第五章，讨论最佳实践。

### 5.1 代码规范

代码规范的相关内容。

### 5.2 项目结构

项目结构的最佳实践。

## 第六章：总结

这是最后一章，对整个内容进行总结。

### 6.1 关键要点

总结关键要点。

### 6.2 下一步

下一步的建议和方向。

## 结论

这是文章的结论部分。通过这个长文章，我们可以测试TOC的粘性定位功能是否正常工作。当用户滚动页面时，TOC应该保持在屏幕右侧的固定位置。

---

**测试说明：**

1. 打开这个页面
2. 滚动页面内容
3. 观察右侧的TOC是否保持在屏幕上的固定位置
4. 点击TOC中的链接，确保导航功能正常

如果TOC能够正常粘性定位，说明修复成功！
