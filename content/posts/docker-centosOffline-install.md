---
title: "Docker 在 Centos7 中离线安装"
date: 2021-03-25T11:12:50+08:00
draft: false
tags: [ "centos7"]
tags_weight: 42
categories: ["docker"]
categories_weight: 42
keywords:
- docker
- 离线安装
- centos7
description: "在 centos 中使用离线安装包安装 docker"
---

# 环境说明

- 操作系统:  Centos 
- Docker 版本:  19.03.8

---

### 离线版本安装

- Docker-ce 版本 YUM源 配置 -  [参考文档](https://docs.docker.com/engine/install/centos/)

- `docker_rpm.tar.gz` 离线文件准备

  ```bash
  sudo yum install --downloadonly --downloaddir=./docker_rpm docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin # 下载离线包，如已机器已安装 docker 可以将 install 改为 reinstall 参数即可
  ```

- 具体操作命令记录

  ```bash
  [root@localhost tools]# ls -lh  
  总用量 58M
  -rw-r--r--. 1 <USER> <GROUP> 58M 9月   9 21:22 docker_rpm.tar.gz	# 此文件请查看百度云盘
  
  # 将软件包解压 并执行安装
  tar xf docker_rpm.tar.gz && cd docker_rpm && rpm -ivh *.rpm --nodeps --force
  
  # 启动服务并设置开机自启
  service docker start \
  && systemctl enable docker \
  && systemctl status docker
  
  # 查看容器版本及服务是否安装正常 
  [root@localhost docker-rpm]# docker info |head -n 10
  Containers: 0
   Running: 0
   Paused: 0
   Stopped: 0
  Images: 0
  Server Version: 18.09.6
  Storage Driver: overlay2
   Backing Filesystem: xfs
   Supports d_type: true
   Native Overlay Diff: true
  ```

---

## 基础优化

> [参考文档](https://www.treesir.pub/post/centos-init-config/#%E4%BC%98%E5%8C%96)
