---
title: "修复 SSH 免密无法连接"
date: 2023-07-24T14:01:29+08:00
draft: false
tags: [ "sshd"]
tags_weight: 22
categories: ["sre", "linux"]
categories_weight: 22
keywords:
- sshd
- ssh
- 免密
- 公私钥
- 连接修复
- 无法连接
description: "修复 SSH 免密无法连接"
---

# 说明

> 我们通常在远程连接目标服务器时，已避免经常性的输入密码，通常会通过 `免密钥` 的方式以解决每次连接多需要输入密码问题，但有的时候我们配置免密钥后，却未能生效，可以尝试使用下述方法进行解决。

## SSH 免密钥方式

1. 生成 SSH 公私钥

   ```bash
   ssh-keygen -b 2048 -t rsa -f ./id_rsa -q -N ""
   ```

2. 公钥 COPY 至目标服务器，完成免密钥

   ```bash
   sshpass -p '{{ .ssh_password }}' \
       ssh-copy-id -p {{ .ssh_port }} -i id_rsa.pub \
       -o StrictHostKeyChecking=no "{{ .ssh_user }}@${host}" -f
   ```

---

# 解决方案

## Plan 1

> `目标` 服务器中执行

```bash
chmod go-w ~/
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_key
```

## Plan 2

> `目标` 服务器中执行

```bash
chmod 700 ~/.ssh/ 
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub 
chmod 644 ~/.ssh/authorized_keys
```

## Plan 3

> 连接失败，有种情况和所使用私钥的权限配置不对有关。`连接机`执行

```bash
chmod 600 id_rsa
```

---

# 总结

> 按照上述三种方案，一般问题多能够得到解决，如果还是不行，请结合服务端日志，进一步分析定位。一般为 `[sshd]` 的配置错误导致。
