---
title: "使用 Docker 部署 Nexus3 私有仓库完整指南"
date: 2020-12-22T09:24:55+08:00
draft: false
tags: [ "nexus3", "docker", "私有仓库"]
tags_weight: 60
categories: ["docker","devops"]
categories_weight: 30
keywords:
- centos7
- nexus3
- 私有仓库
- 版本升级
- devops
- 制品管理
description: "详细介绍如何使用 Docker 部署 Nexus3 私有仓库，包括配置、管理和版本升级的完整流程"
---

Nexus3 是企业级的制品仓库管理平台，能够统一管理各种类型的软件包和依赖。本文将详细介绍如何使用 Docker 部署 Nexus3，以及日常的管理和升级操作。

# Nexus3 产品介绍

## 什么是 Nexus3

Nexus3 是由 Sonatype 公司开发的企业级制品仓库管理工具，它能够统一管理多种类型的软件包仓库，包括：

- **Java 生态**: Maven、Gradle 依赖包
- **前端生态**: npm、Bower 包管理
- **容器镜像**: Docker 镜像仓库
- **系统包管理**: yum、apt、PyPI 包
- **其他格式**: Helm Charts、Go Modules、NuGet 等

通过 Nexus3，企业可以实现统一的制品管理，提高开发效率，降低外部依赖风险。

## 仓库类型详解

Nexus3 支持三种不同类型的仓库，每种类型都有其特定的用途和优势：

### 1. Hosted 仓库（托管仓库）

**用途**: 存储企业内部开发的制品和第三方上传的包
**特点**:
- 完全由企业自主管理
- 可以上传、删除、修改制品
- 适合存储内部开发的组件和库

**使用场景**:
- 企业内部开发的 JAR 包、Docker 镜像
- 第三方供应商提供的私有组件
- 需要定制化的开源组件

### 2. Proxy 仓库（代理仓库）

**用途**: 代理外部公共仓库，提供缓存和加速功能
**工作原理**:
1. 客户端请求依赖包
2. 如果本地缓存存在，直接返回
3. 如果本地不存在，从远程仓库下载并缓存
4. 后续相同请求直接从缓存返回

![代理仓库工作原理](https://cdn.treesir.pub/images/2021/01/14/357d3e0316900d3b63f60effd442356e.png)

**优势**:
- 提高下载速度
- 减少外网带宽消耗
- 提供离线访问能力
- 统一管理外部依赖

### 3. Group 仓库（组合仓库）

**用途**: 将多个仓库组合成一个统一的访问入口
**特点**:
- 可以包含 hosted、proxy、group 类型的仓库
- 支持优先级设置
- 提供统一的访问地址

**重要提示**: Group 仓库采用**自上而下**的匹配策略，一旦找到匹配的制品就停止搜索。因此，仓库的排序非常重要：

![Group 仓库优先级示例](https://cdn.treesir.pub/images/2021/01/14/image-20210114180247413.png)

在上图示例中，如果 `custom` 和 `aliyun` 仓库都包含 `centos:7` 镜像，客户端拉取时会优先获取 `custom` 仓库中的版本。

## 环境要求和准备

### 系统环境

在开始部署之前，请确保您的环境满足以下要求：

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| 操作系统 | CentOS 7.7+ | 推荐使用 CentOS 7 或 8 |
| Docker | 18.09.9+ | 容器运行环境 |
| 内存 | 最少 8GB | Nexus3 是内存密集型应用 |
| 存储 | 最少 100GB | 用于存储制品和数据 |
| CPU | 最少 4 核 | 推荐 8 核以上 |

### 存储规划建议

**重要提示**: 强烈建议使用 LVM（逻辑卷管理）来管理 Nexus3 的存储：

```bash
# 创建 LVM 逻辑卷（示例）
pvcreate /dev/sdb
vgcreate nexus-vg /dev/sdb
lvcreate -L 200G -n nexus-lv nexus-vg
mkfs.xfs /dev/nexus-vg/nexus-lv

# 挂载到 Nexus3 数据目录
mkdir -p /application/nexus3
mount /dev/nexus-vg/nexus-lv /application/nexus3

# 添加到 fstab 实现开机自动挂载
echo "/dev/nexus-vg/nexus-lv /application/nexus3 xfs defaults 0 0" >> /etc/fstab
```

**LVM 的优势**:
- **动态扩展**: 可以在不停机的情况下扩展存储空间
- **快照备份**: 支持创建一致性快照进行备份
- **灵活管理**: 便于存储空间的重新分配

### 网络和防火墙配置

```bash
# 开放必要的端口
firewall-cmd --permanent --add-port=8081/tcp  # Nexus Web UI
firewall-cmd --permanent --add-port=8082/tcp  # Docker Pull
firewall-cmd --permanent --add-port=8083/tcp  # Docker Push
firewall-cmd --reload

# 或者关闭防火墙（仅限测试环境）
systemctl stop firewalld
systemctl disable firewalld
```

## Docker 部署 Nexus3

### 第一步：准备数据目录

创建 Nexus3 的数据存储目录并设置合适的权限：

```bash
# 创建数据目录
mkdir -p /application/nexus3/data

# 设置权限（Nexus3 容器内使用 UID 200）
chown -R 200:200 /application/nexus3/data

# 设置 SELinux 上下文（如果启用了 SELinux）
setsebool -P container_manage_cgroup on
chcon -Rt svirt_sandbox_file_t /application/nexus3/data
```

**安全说明**:
- 避免使用 `chmod 777`，这会带来安全风险
- Nexus3 容器内部使用 UID 200 运行，需要确保目录权限正确

### 第二步：启动 Nexus3 容器

使用以下命令启动 Nexus3 容器：

```bash
docker run -d \
  --name nexus3 \
  --restart=always \
  --ulimit nofile=65536:65536 \
  -p 8081:8081 \
  -p 8082:8082 \
  -p 8083:8083 \
  -e INSTALL4J_ADD_VM_PARAMS="-Xms4g -Xmx4g -XX:MaxDirectMemorySize=6g -Djava.util.prefs.userRoot=/nexus-data/javaprefs" \
  -v /etc/localtime:/etc/localtime:ro \
  -v /application/nexus3/data:/nexus-data \
  sonatype/nexus3:3.27.0
```

**参数说明**:
- `--ulimit nofile=65536:65536`: 增加文件描述符限制
- `-p 8081:8081`: Web 管理界面端口
- `-p 8082:8082`: Docker 仓库拉取端口
- `-p 8083:8083`: Docker 仓库推送端口
- `-Xms4g -Xmx4g`: JVM 堆内存设置（根据服务器配置调整）
- `-XX:MaxDirectMemorySize=6g`: 直接内存大小
- `-Djava.util.prefs.userRoot`: Java 偏好设置存储路径

### 第三步：验证部署

#### 检查容器状态

```bash
# 查看容器运行状态
docker ps | grep nexus3

# 查看容器日志
docker logs -f nexus3

# 检查端口监听
netstat -tlnp | grep -E "8081|8082|8083"
```

#### 等待服务启动

Nexus3 首次启动需要较长时间（通常 2-5 分钟），请耐心等待：

```bash
# 监控启动日志，看到以下信息表示启动成功
docker logs -f nexus3 | grep "Started Sonatype Nexus"
```

#### 获取初始密码

```bash
# 查看初始管理员密码
cat /application/nexus3/data/admin.password

# 或者在容器内查看
docker exec nexus3 cat /nexus-data/admin.password
```

### 第四步：初始化配置

#### 访问 Web 界面

打开浏览器访问：`http://your-server-ip:8081`

#### 完成初始化向导

1. **登录**: 使用用户名 `admin` 和从文件中获取的初始密码
2. **重置密码**: 设置新的管理员密码（建议使用强密码）
3. **匿名访问**: 根据需要选择是否启用匿名访问
4. **完成设置**: 完成初始化向导

![Nexus3 初始化界面](https://cdn.treesir.pub/images/2020/09/10/image-20200910164805785.png)

*图：Nexus3 初始化配置界面*

### 健康检查和监控

#### 创建健康检查脚本

```bash
cat > /usr/local/bin/nexus-health-check.sh << 'EOF'
#!/bin/bash
# Nexus3 健康检查脚本

NEXUS_URL="http://localhost:8081"
HEALTH_ENDPOINT="${NEXUS_URL}/service/rest/v1/status"

# 检查服务状态
if curl -f -s "${HEALTH_ENDPOINT}" > /dev/null; then
    echo "$(date): Nexus3 服务正常"
    exit 0
else
    echo "$(date): Nexus3 服务异常"
    # 可以在这里添加告警逻辑
    exit 1
fi
EOF

chmod +x /usr/local/bin/nexus-health-check.sh
```

#### 设置定时监控

```bash
# 添加到 crontab
echo "*/5 * * * * /usr/local/bin/nexus-health-check.sh >> /var/log/nexus-health.log 2>&1" | crontab -
```

## 配置 Nginx 反向代理

为了提供更好的访问体验和安全性，我们使用 Nginx 作为 Nexus3 的反向代理。这样可以实现：
- 统一的访问入口
- SSL/TLS 终端
- 负载均衡（多实例部署时）
- 访问日志记录

### 第一步：安装和配置 Nginx

#### 安装 Nginx

```bash
# 安装 Nginx
yum install -y nginx

# 创建专用用户
useradd -M -s /sbin/nologin www

# 备份原始配置
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
```

#### 优化 Nginx 主配置

创建针对 Nexus3 优化的 Nginx 配置：

```bash
cat > /etc/nginx/nginx.conf << 'EOF'
# Nginx 主配置文件 - 针对 Nexus3 优化
user www www;
worker_processes auto;

# 错误日志配置
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 工作进程文件描述符限制
worker_rlimit_nofile 65535;

events {
    # 使用 epoll 事件模型（Linux 推荐）
    use epoll;
    worker_connections 8192;
    multi_accept on;
    accept_mutex off;
}

http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 服务器标识
    server_tokens off;

    # 哈希表大小
    server_names_hash_bucket_size 128;
    server_names_hash_max_size 512;

    # 客户端请求配置
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 0;              # 允许大文件上传
    client_body_buffer_size 128k;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # 发送文件优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;

    # 连接超时设置
    keepalive_timeout 65;
    keepalive_requests 1000;

    # 代理配置
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    proxy_buffer_size 64k;
    proxy_buffers 4 64k;
    proxy_busy_buffers_size 128k;
    proxy_temp_file_write_size 128k;
    proxy_intercept_errors off;

    # Gzip 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    gzip_disable "MSIE [1-6]\.";

    # 日志格式定义
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format json_combined escape=json
        '{'
            '"time_local":"$time_local",'
            '"remote_addr":"$remote_addr",'
            '"remote_user":"$remote_user",'
            '"request":"$request",'
            '"status": "$status",'
            '"body_bytes_sent":"$body_bytes_sent",'
            '"request_time":"$request_time",'
            '"http_referrer":"$http_referer",'
            '"http_user_agent":"$http_user_agent",'
            '"http_x_forwarded_for":"$http_x_forwarded_for",'
            '"upstream_addr":"$upstream_addr",'
            '"upstream_status":"$upstream_status",'
            '"upstream_response_time":"$upstream_response_time"'
        '}';

    # 默认访问日志
    access_log /var/log/nginx/access.log main;

    # 包含虚拟主机配置
    include /etc/nginx/conf.d/*.conf;
}
EOF

# 测试配置文件语法
nginx -t
```

### 第二步：配置 Nexus3 虚拟主机

#### 创建 Nexus3 专用配置

```bash
cat > /etc/nginx/conf.d/nexus3.conf << 'EOF'
# Nexus3 反向代理配置

# 上游服务器定义
upstream nexus_web {
    server 127.0.0.1:8081 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream nexus_docker_read {
    server 127.0.0.1:8082 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream nexus_docker_write {
    server 127.0.0.1:8083 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Docker 仓库配置
server {
    listen 80;
    server_name docker.example.com;  # 请修改为您的域名

    # 访问日志
    access_log /var/log/nginx/docker.example.com.log json_combined;
    error_log /var/log/nginx/docker.example.com.error.log;

    # 客户端配置
    client_max_body_size 0;
    chunked_transfer_encoding on;

    # 根据请求方法选择上游服务器
    set $upstream "nexus_docker_write";
    if ($request_method ~ ^(GET|HEAD)$ ) {
        set $upstream "nexus_docker_read";
    }

    # Docker API 搜索请求特殊处理
    if ($request_uri ~ '/v2/_catalog' ) {
        set $upstream "nexus_docker_write";
    }
    if ($request_uri ~ '/v2/search' ) {
        set $upstream "nexus_docker_write";
    }

    location / {
        proxy_pass http://$upstream;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 禁用缓冲以支持大文件上传
        proxy_buffering off;
        proxy_request_buffering off;

        # 超时设置
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 支持 WebSocket（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# Nexus3 Web 管理界面配置
server {
    listen 80;
    server_name nexus.example.com;  # 请修改为您的域名

    # 访问日志
    access_log /var/log/nginx/nexus.example.com.log json_combined;
    error_log /var/log/nginx/nexus.example.com.error.log;

    # 客户端配置
    client_max_body_size 0;
    proxy_max_temp_file_size 0;

    location / {
        proxy_pass http://nexus_web;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 支持 WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # 静态文件下载目录（可选）
    location /download {
        alias /application/download;
        autoindex on;
        autoindex_exact_size off;
        autoindex_localtime on;
    }
}
EOF
```

### 第三步：启动和验证 Nginx

```bash
# 启动 Nginx 服务
systemctl start nginx
systemctl enable nginx

# 检查服务状态
systemctl status nginx

# 验证配置
nginx -t

# 重新加载配置（如果需要修改）
nginx -s reload

# 检查端口监听
netstat -tlnp | grep nginx
```

### SSL/TLS 配置（推荐）

#### 使用 Let's Encrypt 免费证书

```bash
# 安装 Certbot
yum install -y epel-release
yum install -y certbot python2-certbot-nginx

# 获取证书
certbot --nginx -d nexus.example.com -d docker.example.com

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

#### 手动 SSL 配置示例

```nginx
server {
    listen 443 ssl http2;
    server_name nexus.example.com;

    # SSL 证书配置
    ssl_certificate /etc/ssl/certs/nexus.example.com.crt;
    ssl_certificate_key /etc/ssl/private/nexus.example.com.key;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 其他配置同 HTTP 版本
    # ...
}
```



## 配置 Docker 私有仓库

Docker 私有仓库是 Nexus3 最常用的功能之一。我们需要创建三种类型的仓库来实现完整的 Docker 镜像管理。

### 第一步：创建 Blob 存储

Blob 存储是 Nexus3 中用于存储二进制文件的底层存储机制。

#### 创建 Docker 专用 Blob 存储

1. **登录 Nexus3 管理界面**
   - 访问：`http://your-server:8081`
   - 使用管理员账户登录

2. **导航到 Blob Stores**
   - 点击左侧菜单：`Repository` → `Blob Stores`

3. **创建新的 Blob Store**
   - 点击 `Create Blob Store`
   - 选择 `File` 类型
   - 配置参数：

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `docker-blob` | Blob 存储名称 |
| Path | `docker` | 存储路径（相对于 nexus-data） |

![创建 Docker Blob 存储](https://cdn.treesir.pub/images/2020/09/11/image-202009110915461557169a2c54af26adc.png)

*图：创建 Docker 专用的 Blob 存储配置界面*

![Blob 存储配置详情](https://cdn.treesir.pub/images/2020/09/11/image-20200911091722315.png)

*图：Blob 存储的详细配置参数*

### 第二步：创建 Docker Hosted 仓库

Hosted 仓库用于存储企业内部构建的 Docker 镜像。

#### 配置步骤

1. **导航到仓库管理**
   - 点击：`Repository` → `Repositories`

2. **创建新仓库**
   - 点击 `Create repository`
   - 选择 `docker (hosted)`

3. **配置 Hosted 仓库参数**

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `docker-hosted` | 仓库名称 |
| HTTP Port | `8083` | 推送端口 |
| Enable Docker V1 API | ☑️ | 支持 Docker V1 API |
| Blob store | `docker-blob` | 使用前面创建的 Blob 存储 |
| Deployment policy | `Allow redeploy` | 允许重新部署 |

![创建 Docker Hosted 仓库](https://cdn.treesir.pub/images/2020/09/11/image-20200911091224979.png)

*图：选择 Docker Hosted 仓库类型*

![Docker Hosted 配置](https://cdn.treesir.pub/images/2020/09/11/image-20200911091314192.png)

*图：Docker Hosted 仓库的基本配置*

![Docker Hosted 高级配置](https://cdn.treesir.pub/images/2020/09/11/image-20200911091944505.png)

*图：Docker Hosted 仓库的高级配置选项*

### 第三步：创建 Docker Proxy 仓库

Proxy 仓库用于代理外部的 Docker 镜像仓库，提供缓存和加速功能。

#### 配置步骤

1. **创建 Proxy 仓库**
   - 选择 `docker (proxy)`

2. **配置 Proxy 仓库参数**

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `docker-proxy` | 仓库名称 |
| HTTP Port | `8082` | 拉取端口 |
| Remote storage | `https://registry-1.docker.io` | Docker Hub 官方地址 |
| Docker Index | `Use Docker Hub` | 使用 Docker Hub 索引 |
| Blob store | `docker-blob` | 使用相同的 Blob 存储 |

**推荐的代理地址**：
- **Docker Hub**: `https://registry-1.docker.io`
- **阿里云镜像**: `https://7bezldxe.mirror.aliyuncs.com`
- **腾讯云镜像**: `https://mirror.ccs.tencentyun.com`
- **网易云镜像**: `https://hub-mirror.c.163.com`

![创建 Docker Proxy 仓库](https://cdn.treesir.pub/images/2020/09/14/image-20200914141738814.png)

*图：选择 Docker Proxy 仓库类型*

![Docker Proxy 配置](https://cdn.treesir.pub/images/2020/09/14/image-20200914141831948.png)

*图：Docker Proxy 仓库的远程存储配置*

![Docker Proxy 高级配置](https://cdn.treesir.pub/images/2020/09/14/image-20200914141929471.png)

*图：Docker Proxy 仓库的高级配置选项*

### 第四步：创建 Docker Group 仓库

Group 仓库将多个仓库组合成一个统一的访问入口。

#### 配置步骤

1. **创建 Group 仓库**
   - 选择 `docker (group)`

2. **配置 Group 仓库参数**

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `docker-group` | 仓库名称 |
| HTTP Port | `8082` | 统一访问端口 |
| Blob store | `docker-blob` | 使用相同的 Blob 存储 |
| Member repositories | `docker-hosted`, `docker-proxy` | 包含的仓库 |

**重要提示**: 仓库的顺序决定了查找优先级，建议将 `docker-hosted` 放在前面。

![创建 Docker Group 仓库](https://cdn.treesir.pub/images/2020/09/11/image-20200911092506337.png)

*图：选择 Docker Group 仓库类型*

![Docker Group 配置](https://cdn.treesir.pub/images/2020/09/14/image-20200914142809316.png)

*图：Docker Group 仓库的成员配置*

### 第五步：启用 Docker Bearer Token

Docker 仓库需要启用 Bearer Token 认证才能正常工作。

#### 配置步骤

1. **导航到安全设置**
   - 点击：`Security` → `Realms`

2. **启用 Docker Bearer Token Realm**
   - 将 `Docker Bearer Token Realm` 从左侧移动到右侧
   - 点击 `Save` 保存配置

![启用 Docker Bearer Token](https://cdn.treesir.pub/images/2020/09/11/image-20200911110039179.png)

*图：安全域配置界面*

![Docker Bearer Token 配置](https://cdn.treesir.pub/images/2020/09/11/image-20200911110151341.png)

*图：启用 Docker Bearer Token Realm*

### 第六步：验证配置

#### 检查端口监听

```bash
# 检查 Nexus3 端口是否正常监听
netstat -tlnp | grep -E "8081|8082|8083"

# 预期输出：
# tcp 0 0 0.0.0.0:8081 0.0.0.0:* LISTEN 1393/java
# tcp 0 0 0.0.0.0:8082 0.0.0.0:* LISTEN 1393/java
# tcp 0 0 0.0.0.0:8083 0.0.0.0:* LISTEN 1393/java
```

#### 测试仓库连通性

```bash
# 测试 Web 界面
curl -I http://localhost:8081

# 测试 Docker 仓库
curl -I http://localhost:8082/v2/

# 预期返回 401 Unauthorized（需要认证）
```



### 第七步：查看配置结果

完成所有配置后，您应该能在 Nexus3 管理界面中看到以下仓库：

![所有 Docker 仓库展示](https://cdn.treesir.pub/images/2020/09/11/image-20200911104933579.png)

*图：完成配置后的 Docker 仓库列表*

## Docker 客户端配置和测试

### 第一步：配置 Docker 客户端

#### 配置 Docker Daemon

在需要使用私有仓库的客户端机器上配置 Docker：

```bash
# 创建或编辑 Docker 配置文件
sudo mkdir -p /etc/docker

cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 10,
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ],
  "registry-mirrors": [
    "http://docker.example.com"
  ],
  "insecure-registries": [
    "docker.example.com",
    "nexus.example.com:8082",
    "nexus.example.com:8083"
  ]
}
EOF
```

**配置说明**:
- `registry-mirrors`: 配置镜像加速器
- `insecure-registries`: 允许使用 HTTP 协议的私有仓库
- `max-concurrent-downloads/uploads`: 并发下载/上传数量

#### 配置 DNS 解析

如果使用域名访问，需要配置 DNS 解析：

```bash
# 方法一：修改 /etc/hosts 文件
echo "************* docker.example.com nexus.example.com" >> /etc/hosts

# 方法二：配置 DNS 服务器（推荐生产环境）
# 在您的 DNS 服务器中添加 A 记录
```

### 第二步：重启 Docker 服务

```bash
# 重启 Docker 服务
sudo systemctl restart docker

# 检查服务状态
sudo systemctl status docker

# 验证配置
docker info | grep -A 10 "Registry Mirrors"
```

### 第三步：登录私有仓库

```bash
# 登录到私有仓库
docker login docker.example.com -u admin

# 输入密码（您在初始化时设置的密码）
# 成功后会显示：Login Succeeded
```

### 第四步：测试镜像推送和拉取

#### 推送镜像测试

```bash
# 1. 拉取一个公共镜像
docker pull alpine:latest

# 2. 为镜像打标签
docker tag alpine:latest docker.example.com/alpine:latest

# 3. 推送到私有仓库
docker push docker.example.com/alpine:latest

# 4. 验证推送结果
echo "推送完成，可以在 Nexus3 Web 界面查看"
```

#### 拉取镜像测试

```bash
# 1. 删除本地镜像
docker rmi docker.example.com/alpine:latest
docker rmi alpine:latest

# 2. 从私有仓库拉取
docker pull docker.example.com/alpine:latest

# 3. 验证拉取结果
docker images | grep alpine
```

![镜像推送成功示例](https://cdn.treesir.pub/images/2020/09/11/image-20200911110428718fa9042b0fd5263d1.png)

*图：成功推送镜像到私有仓库的示例*

### 第五步：批量镜像迁移

#### 创建镜像迁移脚本

```bash
cat > /usr/local/bin/docker-migrate.sh << 'EOF'
#!/bin/bash
# Docker 镜像批量迁移脚本

PRIVATE_REGISTRY="docker.example.com"
IMAGES_FILE="/tmp/images.txt"

# 获取本地所有镜像
docker images --format "table {{.Repository}}:{{.Tag}}" | grep -v "REPOSITORY:TAG" > $IMAGES_FILE

echo "开始迁移镜像到私有仓库..."

while IFS= read -r image; do
    if [[ "$image" != *"$PRIVATE_REGISTRY"* ]]; then
        echo "处理镜像: $image"

        # 为镜像打标签
        new_tag="$PRIVATE_REGISTRY/$image"
        docker tag "$image" "$new_tag"

        # 推送到私有仓库
        if docker push "$new_tag"; then
            echo "✅ 成功推送: $new_tag"
        else
            echo "❌ 推送失败: $new_tag"
        fi

        # 删除临时标签
        docker rmi "$new_tag" 2>/dev/null
    fi
done < "$IMAGES_FILE"

echo "镜像迁移完成！"
rm -f "$IMAGES_FILE"
EOF

chmod +x /usr/local/bin/docker-migrate.sh
```

#### 使用迁移脚本

```bash
# 执行镜像迁移
/usr/local/bin/docker-migrate.sh
```

### 故障排除

#### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| `x509: certificate signed by unknown authority` | SSL 证书问题 | 添加到 `insecure-registries` 或配置正确的证书 |
| `dial tcp: lookup docker.example.com: no such host` | DNS 解析失败 | 检查 DNS 配置或 /etc/hosts 文件 |
| `unauthorized: authentication required` | 认证失败 | 检查用户名密码，确保已启用 Docker Bearer Token |
| `denied: requested access to the resource is denied` | 权限不足 | 检查用户权限和仓库访问策略 |

#### 调试命令

```bash
# 查看 Docker 配置
docker info

# 测试网络连通性
telnet docker.example.com 80

# 查看 Docker 日志
journalctl -u docker.service -f

# 测试仓库 API
curl -v http://docker.example.com/v2/
```

## Nexus3 版本升级指南

Nexus3 的定期升级是维护系统安全性和稳定性的重要措施。Sonatype 官方建议定期更新 Nexus3 版本以获得最新的安全补丁和功能改进。

### 升级前准备

#### 第一步：备份数据

**重要提示**: 升级前必须备份数据，以防升级失败时能够快速恢复。

```bash
# 停止 Nexus3 服务
docker stop nexus3

# 创建数据备份
BACKUP_DIR="/backup/nexus3-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 备份数据目录
cp -r /application/nexus3/data "$BACKUP_DIR/"

# 备份 Docker 配置
docker inspect nexus3 > "$BACKUP_DIR/container-config.json"

# 创建压缩备份（可选）
tar -czf "$BACKUP_DIR.tar.gz" -C /application/nexus3 data

echo "备份完成: $BACKUP_DIR"
```

#### 第二步：检查版本兼容性

```bash
# 查看当前版本
docker exec nexus3 cat /opt/sonatype/nexus/VERSION 2>/dev/null || echo "容器未运行"

# 查看可用版本
curl -s https://registry.hub.docker.com/v2/repositories/sonatype/nexus3/tags/ | \
  jq -r '.results[].name' | head -10
```

**版本选择建议**:
- 优先选择 LTS（长期支持）版本
- 避免跨越多个大版本升级
- 查看 [官方发布说明](https://help.sonatype.com/repomanager3/product-information/release-notes) 了解重大变更

#### 第三步：制定升级计划

| 升级类型 | 风险等级 | 建议策略 |
|----------|----------|----------|
| 补丁版本 (3.27.0 → 3.27.1) | 低 | 可直接升级 |
| 次要版本 (3.27.x → 3.28.x) | 中 | 建议测试环境验证 |
| 主要版本 (3.x → 4.x) | 高 | 必须详细测试和规划 |

### 升级执行

#### 标准升级流程

以下示例将 Nexus3 从 3.28.1 升级到 3.29.2：

```bash
#!/bin/bash
# Nexus3 升级脚本

set -e  # 遇到错误立即退出

# 配置变量
OLD_VERSION="3.28.1"
NEW_VERSION="3.29.2"
CONTAINER_NAME="nexus3"
BACKUP_DIR="/backup/nexus3-upgrade-$(date +%Y%m%d-%H%M%S)"

echo "开始 Nexus3 升级流程..."
echo "当前版本: $OLD_VERSION"
echo "目标版本: $NEW_VERSION"

# 1. 创建备份
echo "步骤 1: 创建数据备份..."
mkdir -p "$BACKUP_DIR"
docker stop "$CONTAINER_NAME"
cp -r /application/nexus3/data "$BACKUP_DIR/"
echo "备份完成: $BACKUP_DIR"

# 2. 重命名旧容器
echo "步骤 2: 保留旧容器..."
docker rename "$CONTAINER_NAME" "${CONTAINER_NAME}_${OLD_VERSION}_backup"

# 3. 拉取新镜像
echo "步骤 3: 拉取新版本镜像..."
docker pull "sonatype/nexus3:$NEW_VERSION"

# 4. 启动新容器
echo "步骤 4: 启动新版本容器..."
docker run -d \
  --name "$CONTAINER_NAME" \
  --restart=always \
  --ulimit nofile=65536:65536 \
  -p 8081:8081 \
  -p 8082:8082 \
  -p 8083:8083 \
  -e INSTALL4J_ADD_VM_PARAMS="-Xms4g -Xmx4g -XX:MaxDirectMemorySize=6g -Djava.util.prefs.userRoot=/nexus-data/javaprefs" \
  -v /etc/localtime:/etc/localtime:ro \
  -v /application/nexus3/data:/nexus-data \
  "sonatype/nexus3:$NEW_VERSION"

# 5. 等待服务启动
echo "步骤 5: 等待服务启动..."
sleep 30

# 6. 检查服务状态
echo "步骤 6: 检查服务状态..."
for i in {1..30}; do
    if curl -f -s http://localhost:8081/service/rest/v1/status > /dev/null; then
        echo "✅ Nexus3 服务启动成功"
        break
    fi
    echo "等待服务启动... ($i/30)"
    sleep 10
done

# 7. 验证升级结果
echo "步骤 7: 验证升级结果..."
NEW_RUNNING_VERSION=$(docker exec "$CONTAINER_NAME" cat /opt/sonatype/nexus/VERSION)
echo "当前运行版本: $NEW_RUNNING_VERSION"

if [[ "$NEW_RUNNING_VERSION" == "$NEW_VERSION" ]]; then
    echo "✅ 升级成功！"
    echo "可以通过以下命令删除备份容器:"
    echo "docker rm ${CONTAINER_NAME}_${OLD_VERSION}_backup"
else
    echo "❌ 升级失败，版本不匹配"
    exit 1
fi
```

#### 手动升级步骤

如果不使用脚本，可以按以下步骤手动执行：

```bash
# 1. 停止当前容器
docker stop nexus3

# 2. 重命名容器（保留备份）
docker rename nexus3 nexus3_backup

# 3. 拉取新版本镜像
docker pull sonatype/nexus3:3.29.2

# 4. 启动新容器
docker run -d \
  --name nexus3 \
  --restart=always \
  --ulimit nofile=65536:65536 \
  -p 8081:8081 \
  -p 8082:8082 \
  -p 8083:8083 \
  -e INSTALL4J_ADD_VM_PARAMS="-Xms4g -Xmx4g -XX:MaxDirectMemorySize=6g" \
  -v /etc/localtime:/etc/localtime:ro \
  -v /application/nexus3/data:/nexus-data \
  sonatype/nexus3:3.29.2

# 5. 监控启动过程
docker logs -f nexus3
```

### 升级后验证

#### 功能验证清单

```bash
# 1. 检查服务状态
curl -I http://localhost:8081

# 2. 验证 Web 界面访问
curl -s http://localhost:8081 | grep -q "Nexus Repository Manager"

# 3. 检查仓库列表
curl -u admin:password http://localhost:8081/service/rest/v1/repositories

# 4. 测试 Docker 仓库
docker pull alpine:latest
docker tag alpine:latest localhost:8082/alpine:test
docker push localhost:8083/alpine:test

# 5. 验证数据完整性
# 检查关键仓库和配置是否正常
```

#### 性能监控

```bash
# 监控容器资源使用
docker stats nexus3

# 检查日志中的错误
docker logs nexus3 | grep -i error

# 监控磁盘使用
df -h /application/nexus3/data
```

![升级成功示例](https://cdn.treesir.pub/images/2021/01/14/image-20210114092823923.png)

*图：Nexus3 升级成功后的日志输出*

### 回滚策略

如果升级失败，可以快速回滚到之前的版本：

```bash
#!/bin/bash
# Nexus3 回滚脚本

echo "开始回滚 Nexus3..."

# 1. 停止新容器
docker stop nexus3
docker rm nexus3

# 2. 恢复数据（如果需要）
# cp -r /backup/nexus3-backup/data/* /application/nexus3/data/

# 3. 启动备份容器
docker rename nexus3_backup nexus3
docker start nexus3

# 4. 验证回滚结果
sleep 30
if curl -f -s http://localhost:8081/service/rest/v1/status > /dev/null; then
    echo "✅ 回滚成功"
else
    echo "❌ 回滚失败，请检查日志"
fi
```

### 升级最佳实践

#### 1. 定期升级策略

```bash
# 创建升级检查脚本
cat > /usr/local/bin/nexus-version-check.sh << 'EOF'
#!/bin/bash
# 检查 Nexus3 新版本

CURRENT_VERSION=$(docker exec nexus3 cat /opt/sonatype/nexus/VERSION 2>/dev/null)
LATEST_VERSION=$(curl -s https://api.github.com/repos/sonatype/nexus-public/releases/latest | jq -r '.tag_name' | sed 's/release-//')

echo "当前版本: $CURRENT_VERSION"
echo "最新版本: $LATEST_VERSION"

if [[ "$CURRENT_VERSION" != "$LATEST_VERSION" ]]; then
    echo "⚠️  发现新版本，建议升级"
else
    echo "✅ 已是最新版本"
fi
EOF

chmod +x /usr/local/bin/nexus-version-check.sh

# 设置定期检查
echo "0 9 * * 1 /usr/local/bin/nexus-version-check.sh | mail -s 'Nexus3 版本检查' <EMAIL>" | crontab -
```

#### 2. 升级注意事项

- **测试环境验证**: 先在测试环境进行升级验证
- **维护窗口**: 选择业务低峰期进行升级
- **通知机制**: 提前通知用户升级计划
- **监控告警**: 升级后加强监控，及时发现问题
- **文档记录**: 记录升级过程和遇到的问题

#### 3. 清理工作

升级成功并稳定运行一段时间后，可以清理备份：

```bash
# 删除备份容器（确认升级成功后）
docker rm nexus3_backup

# 清理旧镜像
docker image prune -f

# 清理过期备份（保留最近3个备份）
find /backup -name "nexus3-*" -type d | sort -r | tail -n +4 | xargs rm -rf
```

## 其他类型私有仓库配置

Nexus3 支持多种包管理格式，可以为企业提供统一的制品管理平台。以下介绍几种常用的私有仓库配置方法。

> **官方文档**: [Nexus3 支持的仓库格式](https://help.sonatype.com/repomanager3/formats)

## Yum 私有仓库配置

Yum 私有仓库可以为 CentOS/RHEL 系统提供 RPM 包管理服务，特别适合企业内网环境。

### 第一步：创建 Yum Blob 存储

#### 配置 Blob Store

1. **导航到 Blob Stores**
   - 点击：`Repository` → `Blob Stores`

2. **创建新的 Blob Store**
   - 点击 `Create Blob Store`
   - 配置参数：

| 参数 | 值 | 说明 |
|------|-----|------|
| Type | `File` | 文件存储类型 |
| Name | `yum-blob` | Blob 存储名称 |
| Path | `yum` | 存储路径 |

![创建 Yum Blob 存储](https://cdn.treesir.pub/images/2020/09/10/image-20200910170348196.png)

*图：创建 Yum 专用的 Blob 存储*

![Yum Blob 存储配置](https://cdn.treesir.pub/images/2020/09/10/image-20200910170438617.png)

*图：Yum Blob 存储的详细配置*

### 第二步：创建 Yum Hosted 仓库

#### 配置 Hosted 仓库

1. **创建新仓库**
   - 点击：`Repository` → `Repositories` → `Create repository`
   - 选择 `yum (hosted)`

2. **配置仓库参数**

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `yum-hosted` | 仓库名称 |
| Blob store | `yum-blob` | 使用创建的 Blob 存储 |
| Deployment policy | `Allow redeploy` | 允许重新部署 |
| Repodata Depth | `3` | **重要**: 仓库数据深度 |

![创建 Yum Hosted 仓库](https://cdn.treesir.pub/images/2020/09/10/image-20200910170153343.png)

*图：选择 Yum Hosted 仓库类型*

![Yum Hosted 配置](https://cdn.treesir.pub/images/2020/09/10/image-20200910180549889.png)

*图：Yum Hosted 仓库的详细配置*

#### 重要配置说明

**Repodata Depth（仓库数据深度）**是 Yum 仓库的关键配置：

![Repodata Depth 说明](https://cdn.treesir.pub/images/2020/09/10/image-20200910180656801.png)

*图：官方文档中关于 Repodata Depth 的说明*

**深度配置对应关系**:
- **深度 0**: `/repository/yum-hosted/`
- **深度 1**: `/repository/yum-hosted/7/`
- **深度 2**: `/repository/yum-hosted/7/os/`
- **深度 3**: `/repository/yum-hosted/7/os/x86_64/`

**推荐配置**: 使用深度 3，符合标准的 CentOS 仓库结构。

### 第三步：准备 RPM 包

#### 从 CentOS ISO 提取 RPM 包

使用 CentOS 7 完整版 ISO 作为示例：

```bash
# 1. 挂载 CentOS ISO 镜像
sudo mkdir -p /mnt/centos-iso
sudo mount -o loop CentOS-7-x86_64-Everything-2003.iso /mnt/centos-iso

# 2. 创建本地工作目录
mkdir -p /tmp/centos7-rpms
cd /tmp/centos7-rpms

# 3. 复制 RPM 包
cp /mnt/centos-iso/Packages/*.rpm ./

# 4. 验证 RPM 包数量
echo "总计 RPM 包数量: $(ls *.rpm | wc -l)"

# 5. 卸载 ISO（可选）
sudo umount /mnt/centos-iso
```

#### 组织 RPM 包结构

```bash
# 创建标准的 YUM 仓库目录结构
mkdir -p /tmp/yum-upload/{7/os/x86_64,8/os/x86_64}

# 将 CentOS 7 的包放到对应目录
mv /tmp/centos7-rpms/*.rpm /tmp/yum-upload/7/os/x86_64/

# 添加自定义 RPM 包（如果有）
# cp /path/to/custom/*.rpm /tmp/yum-upload/7/os/x86_64/
```

### 第四步：批量上传 RPM 包

#### 创建上传脚本

```bash
cat > /usr/local/bin/yum-upload.sh << 'EOF'
#!/bin/bash
# Yum 仓库 RPM 包批量上传脚本

set -e

# 配置变量
NEXUS_URL="http://nexus.example.com:8081"
REPOSITORY="yum-hosted"
USERNAME="admin"
PASSWORD="your-password"
RPM_DIR="/tmp/yum-upload"

# 安全提示
echo "⚠️  注意：此脚本将上传 RPM 包到 Nexus3"
echo "仓库: $REPOSITORY"
echo "目录: $RPM_DIR"
read -p "确认继续？(y/N): " confirm

if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
    echo "操作已取消"
    exit 0
fi

# 关闭命令历史记录（安全考虑）
set +H

# 统计信息
total_files=0
uploaded_files=0
failed_files=0

# 递归上传函数
upload_rpms() {
    local base_dir="$1"
    local relative_path="$2"

    for item in "$base_dir"/*; do
        if [[ -d "$item" ]]; then
            # 递归处理子目录
            local dirname=$(basename "$item")
            upload_rpms "$item" "$relative_path/$dirname"
        elif [[ -f "$item" && "$item" == *.rpm ]]; then
            # 上传 RPM 文件
            local filename=$(basename "$item")
            local upload_path="$relative_path/$filename"

            echo "上传: $upload_path"
            total_files=$((total_files + 1))

            if curl -f -u "$USERNAME:$PASSWORD" \
                    --upload-file "$item" \
                    "$NEXUS_URL/repository/$REPOSITORY$upload_path"; then
                uploaded_files=$((uploaded_files + 1))
                echo "✅ 成功: $filename"
            else
                failed_files=$((failed_files + 1))
                echo "❌ 失败: $filename"
            fi
        fi
    done
}

# 开始上传
echo "开始上传 RPM 包..."
upload_rpms "$RPM_DIR" ""

# 输出统计信息
echo ""
echo "上传完成！"
echo "总文件数: $total_files"
echo "成功上传: $uploaded_files"
echo "失败数量: $failed_files"

# 触发仓库索引重建
echo "触发仓库索引重建..."
curl -X POST -u "$USERNAME:$PASSWORD" \
     "$NEXUS_URL/service/rest/v1/repositories/$REPOSITORY/rebuild-index"

echo "✅ 索引重建已触发，请等待完成"
EOF

chmod +x /usr/local/bin/yum-upload.sh
```

#### 执行上传

```bash
# 修改脚本中的配置信息
vim /usr/local/bin/yum-upload.sh

# 执行上传
/usr/local/bin/yum-upload.sh
```

#### 手动上传示例

```bash
# 设置变量
NEXUS_URL="http://nexus.example.com:8081"
REPOSITORY="yum-hosted"
USERNAME="admin"
PASSWORD="your-password"

# 上传单个 RPM 包
curl -v -u "$USERNAME:$PASSWORD" \
     --upload-file /tmp/yum-upload/7/os/x86_64/vim-enhanced-7.4.629-8.el7_9.x86_64.rpm \
     "$NEXUS_URL/repository/$REPOSITORY/7/os/x86_64/vim-enhanced-7.4.629-8.el7_9.x86_64.rpm"

# 批量上传（简单版本）
cd /tmp/yum-upload/7/os/x86_64
for rpm in *.rpm; do
    echo "上传: $rpm"
    curl -f -u "$USERNAME:$PASSWORD" \
         --upload-file "$rpm" \
         "$NEXUS_URL/repository/$REPOSITORY/7/os/x86_64/$rpm"
done
```

### 第五步：重建仓库索引

上传完成后需要重建 YUM 仓库索引：

#### 自动重建

```bash
# 通过 API 触发索引重建
curl -X POST -u admin:password \
     http://nexus.example.com:8081/service/rest/v1/repositories/yum-hosted/rebuild-index
```

#### 手动重建

1. **登录 Nexus3 管理界面**
2. **导航到仓库管理**: `Repository` → `Repositories`
3. **选择 yum-hosted 仓库**
4. **点击 "Rebuild index" 按钮**

![重建索引操作](https://cdn.treesir.pub/images/2020/09/10/image-20200910182853407.png)

*图：手动触发仓库索引重建*

![索引重建进度](https://cdn.treesir.pub/images/2020/09/10/image-20200910182914592.png)

*图：索引重建任务的执行进度*

### 第六步：客户端配置

#### 配置 YUM 客户端

在需要使用私有仓库的 CentOS/RHEL 系统上配置：

```bash
# 1. 备份原有仓库配置
sudo mv /etc/yum.repos.d /etc/yum.repos.d.backup
sudo mkdir -p /etc/yum.repos.d

# 2. 创建 Nexus3 仓库配置
cat > /etc/yum.repos.d/nexus.repo << 'EOF'
[nexus-yum]
name=Nexus YUM Repository
baseurl=http://nexus.example.com:8081/repository/yum-hosted/$releasever/os/$basearch/
enabled=1
gpgcheck=0
priority=1
EOF

# 3. 清理并重建缓存
sudo yum clean all
sudo yum makecache

# 4. 验证仓库配置
yum repolist
```

#### 高级配置选项

```bash
# 创建更完整的仓库配置
cat > /etc/yum.repos.d/nexus-complete.repo << 'EOF'
[nexus-base]
name=Nexus Base Repository
baseurl=http://nexus.example.com:8081/repository/yum-hosted/$releasever/os/$basearch/
enabled=1
gpgcheck=0
priority=1
metadata_expire=300
keepcache=1

[nexus-updates]
name=Nexus Updates Repository
baseurl=http://nexus.example.com:8081/repository/yum-updates/$releasever/os/$basearch/
enabled=1
gpgcheck=0
priority=2

[nexus-extras]
name=Nexus Extras Repository
baseurl=http://nexus.example.com:8081/repository/yum-extras/$releasever/os/$basearch/
enabled=0
gpgcheck=0
priority=3
EOF
```

#### 测试安装

```bash
# 搜索可用包
yum search vim

# 安装软件包
sudo yum install -y vim-enhanced

# 查看包信息
yum info vim-enhanced

# 列出所有可用包
yum list available | head -20
```

![YUM 客户端测试](https://cdn.treesir.pub/images/2020/09/10/image-20200910183110431.png)

*图：YUM 客户端成功连接私有仓库的示例*

### 故障排除

#### 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| `repomd.xml` 不存在 | 索引未重建 | 手动触发索引重建 |
| 包下载失败 | 路径深度配置错误 | 检查 Repodata Depth 设置 |
| 权限被拒绝 | 认证失败 | 检查用户名密码和权限 |
| 仓库不可用 | 网络连接问题 | 检查网络和防火墙设置 |

#### 调试命令

```bash
# 测试仓库连通性
curl -I http://nexus.example.com:8081/repository/yum-hosted/7/os/x86_64/repodata/repomd.xml

# 查看详细错误信息
yum --verbose install package-name

# 检查仓库配置
yum-config-manager --dump nexus-yum

# 清理缓存
sudo yum clean all && sudo yum makecache
```



## Helm Chart 私有仓库配置

Helm Chart 私有仓库可以为 Kubernetes 应用提供统一的包管理服务，便于企业内部应用的分发和版本管理。

### 第一步：创建 Helm Blob 存储

#### 配置 Blob Store

1. **创建专用 Blob Store**
   - Name: `helm-blob`
   - Path: `helm`

![创建 Helm Blob 存储](https://cdn.treesir.pub/images/2020/09/18/image-20200918103554963.png)

*图：创建 Helm 专用的 Blob 存储*

### 第二步：创建 Helm Hosted 仓库

#### 配置步骤

1. **创建新仓库**
   - 选择 `helm (hosted)`

2. **配置仓库参数**

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `helm-hosted` | 仓库名称 |
| Blob store | `helm-blob` | 使用创建的 Blob 存储 |
| Deployment policy | `Allow redeploy` | 允许重新部署 |

![选择 Helm Hosted 类型](https://cdn.treesir.pub/images/2020/09/18/image-20200918103410484.png)

*图：选择 Helm Hosted 仓库类型*

![Helm Hosted 基本配置](https://cdn.treesir.pub/images/2020/09/18/image-20200918103456336.png)

*图：Helm Hosted 仓库的基本配置*

![Helm Hosted 存储配置](https://cdn.treesir.pub/images/2020/09/18/image-20200918103752200.png)

*图：Helm Hosted 仓库的存储配置*

![Helm Hosted 完成配置](https://cdn.treesir.pub/images/2020/09/18/image-20200918103955596.png)

*图：Helm Hosted 仓库配置完成*

### 第三步：创建 Helm Proxy 仓库（可选）

虽然上面的截图中没有演示 Proxy 仓库配置，但在实际使用中，Proxy 仓库非常有用：

#### 配置 Helm Proxy 仓库

```bash
# 常用的 Helm Chart 仓库地址
# 官方稳定仓库: https://charts.helm.sh/stable
# Bitnami 仓库: https://charts.bitnami.com/bitnami
# Prometheus 社区: https://prometheus-community.github.io/helm-charts
```

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `helm-proxy-stable` | 仓库名称 |
| Remote storage | `https://charts.helm.sh/stable` | 远程仓库地址 |
| Blob store | `helm-blob` | 使用相同的 Blob 存储 |

### 第四步：上传 Helm Charts

#### 方法一：Web 界面上传

1. **导航到仓库**
   - 点击：`Browse` → 选择 `helm-hosted`

2. **上传 Chart**
   - 点击 `Upload component`
   - 选择 `.tgz` 格式的 Helm Chart 文件

![Web 界面上传 Helm Chart](https://cdn.treesir.pub/images/2020/09/18/image-20200918145618916.png)

*图：通过 Web 界面上传 Helm Chart*

![上传 Chart 文件](https://cdn.treesir.pub/images/2020/09/18/image-20200918145646107.png)

*图：选择并上传 Helm Chart 文件*

#### 方法二：命令行上传

```bash
# 基本上传命令
curl -u "admin:password" \
     --upload-file ./my-chart-1.0.0.tgz \
     http://nexus.example.com:8081/repository/helm-hosted/

# 批量上传脚本
cat > /usr/local/bin/helm-upload.sh << 'EOF'
#!/bin/bash
# Helm Chart 批量上传脚本

NEXUS_URL="http://nexus.example.com:8081"
REPOSITORY="helm-hosted"
USERNAME="admin"
PASSWORD="your-password"
CHARTS_DIR="./charts"

if [[ ! -d "$CHARTS_DIR" ]]; then
    echo "错误: 目录 $CHARTS_DIR 不存在"
    exit 1
fi

echo "开始上传 Helm Charts..."

for chart in "$CHARTS_DIR"/*.tgz; do
    if [[ -f "$chart" ]]; then
        chart_name=$(basename "$chart")
        echo "上传: $chart_name"

        if curl -f -u "$USERNAME:$PASSWORD" \
                --upload-file "$chart" \
                "$NEXUS_URL/repository/$REPOSITORY/$chart_name"; then
            echo "✅ 成功: $chart_name"
        else
            echo "❌ 失败: $chart_name"
        fi
    fi
done

echo "上传完成！"
EOF

chmod +x /usr/local/bin/helm-upload.sh
```

#### 方法三：使用 Helm 插件

```bash
# 安装 Helm push 插件
helm plugin install https://github.com/chartmuseum/helm-push

# 添加仓库
helm repo add nexus http://nexus.example.com:8081/repository/helm-hosted/ \
  --username admin --password your-password

# 推送 Chart
helm push my-chart-1.0.0.tgz nexus
```

### 第五步：客户端配置和使用

#### 添加 Helm 仓库

```bash
# 添加私有仓库
helm repo add nexus-helm http://nexus.example.com:8081/repository/helm-hosted/

# 如果需要认证
helm repo add nexus-helm http://nexus.example.com:8081/repository/helm-hosted/ \
  --username admin --password your-password

# 更新仓库索引
helm repo update

# 验证仓库
helm repo list
```

#### 搜索和安装 Charts

```bash
# 搜索可用的 Charts
helm search repo nexus-helm

# 查看 Chart 详情
helm show chart nexus-helm/my-chart

# 安装 Chart
helm install my-release nexus-helm/my-chart

# 升级 Chart
helm upgrade my-release nexus-helm/my-chart --version 1.1.0
```

#### 开发和发布流程

```bash
# 1. 创建新的 Chart
helm create my-new-chart

# 2. 编辑 Chart 内容
# 修改 Chart.yaml, values.yaml, templates/ 等

# 3. 验证 Chart
helm lint my-new-chart/

# 4. 打包 Chart
helm package my-new-chart/

# 5. 上传到私有仓库
curl -u admin:password \
     --upload-file my-new-chart-1.0.0.tgz \
     http://nexus.example.com:8081/repository/helm-hosted/

# 6. 更新仓库索引
helm repo update
```



## PyPI 私有仓库配置

PyPI 私有仓库可以为 Python 项目提供包管理服务，支持企业内部 Python 包的分发和版本管理。

### 第一步：创建 PyPI Blob 存储

#### 配置 Blob Store

1. **创建专用 Blob Store**
   - Name: `pypi-blob`
   - Path: `pypi`

![创建 PyPI Blob 存储](https://cdn.treesir.pub/images/2020/09/18/image-20200918104237876.png)

*图：创建 PyPI 专用的 Blob 存储*

### 第二步：创建 PyPI Hosted 仓库

#### 配置步骤

1. **创建新仓库**
   - 选择 `pypi (hosted)`

2. **配置仓库参数**

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `pypi-hosted` | 仓库名称 |
| Blob store | `pypi-blob` | 使用创建的 Blob 存储 |
| Deployment policy | `Allow redeploy` | 允许重新部署 |

![创建 PyPI Hosted 仓库](https://cdn.treesir.pub/images/2020/09/18/image-20200918104347358.png)

*图：创建 PyPI Hosted 仓库配置*

### 第三步：创建 PyPI Proxy 仓库

为了代理官方 PyPI 仓库，提供缓存和加速功能：

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `pypi-proxy` | 仓库名称 |
| Remote storage | `https://pypi.org/` | PyPI 官方地址 |
| Blob store | `pypi-blob` | 使用相同的 Blob 存储 |

**国内镜像源推荐**:
- 阿里云: `https://mirrors.aliyun.com/pypi/simple/`
- 清华大学: `https://pypi.tuna.tsinghua.edu.cn/simple/`
- 豆瓣: `https://pypi.douban.com/simple/`

### 第四步：创建 PyPI Group 仓库

Group 仓库将 hosted 和 proxy 仓库组合成统一入口：

![创建 PyPI Group 仓库](https://cdn.treesir.pub/images/2020/09/18/image-20200918104723703.png)

*图：创建 PyPI Group 仓库，组合多个仓库*

#### 配置 Group 仓库

| 参数 | 值 | 说明 |
|------|-----|------|
| Name | `pypi-group` | 仓库名称 |
| Blob store | `pypi-blob` | 使用相同的 Blob 存储 |
| Member repositories | `pypi-hosted`, `pypi-proxy` | 包含的仓库 |

**重要**: 将 `pypi-hosted` 放在前面，确保内部包优先级更高。

![PyPI Group 仓库地址](https://cdn.treesir.pub/images/2020/09/18/image-20200918104736891.png)

*图：配置完成后使用 Group 仓库地址*

### 第五步：上传 Python 包

#### 方法一：使用 twine 上传

```bash
# 1. 安装 twine 工具
pip install twine

# 2. 构建 Python 包
python setup.py sdist bdist_wheel

# 3. 上传到私有仓库
twine upload --repository-url http://nexus.example.com:8081/repository/pypi-hosted/ \
             dist/* \
             --username admin \
             --password your-password
```

#### 方法二：配置 .pypirc 文件

```bash
# 创建 .pypirc 配置文件
cat > ~/.pypirc << 'EOF'
[distutils]
index-servers =
    nexus
    nexus-hosted

[nexus]
repository = http://nexus.example.com:8081/repository/pypi-group/
username = admin
password = your-password

[nexus-hosted]
repository = http://nexus.example.com:8081/repository/pypi-hosted/
username = admin
password = your-password
EOF

# 设置文件权限
chmod 600 ~/.pypirc

# 使用配置文件上传
twine upload --repository nexus-hosted dist/*
```

#### 方法三：批量上传脚本

```bash
cat > /usr/local/bin/pypi-upload.sh << 'EOF'
#!/bin/bash
# PyPI 包批量上传脚本

NEXUS_URL="http://nexus.example.com:8081/repository/pypi-hosted/"
USERNAME="admin"
PASSWORD="your-password"
DIST_DIR="./dist"

if [[ ! -d "$DIST_DIR" ]]; then
    echo "错误: 目录 $DIST_DIR 不存在"
    echo "请先运行: python setup.py sdist bdist_wheel"
    exit 1
fi

echo "开始上传 Python 包..."

# 上传所有 .tar.gz 和 .whl 文件
for package in "$DIST_DIR"/*.{tar.gz,whl}; do
    if [[ -f "$package" ]]; then
        package_name=$(basename "$package")
        echo "上传: $package_name"

        if twine upload --repository-url "$NEXUS_URL" \
                        --username "$USERNAME" \
                        --password "$PASSWORD" \
                        "$package"; then
            echo "✅ 成功: $package_name"
        else
            echo "❌ 失败: $package_name"
        fi
    fi
done

echo "上传完成！"
EOF

chmod +x /usr/local/bin/pypi-upload.sh
```

### 第六步：客户端配置

#### 配置 pip 使用私有仓库

```bash
# 方法一：命令行指定
pip install --index-url http://nexus.example.com:8081/repository/pypi-group/simple/ \
            --trusted-host nexus.example.com \
            your-package

# 方法二：配置文件
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = http://nexus.example.com:8081/repository/pypi-group/simple/
trusted-host = nexus.example.com

[install]
trusted-host = nexus.example.com
EOF
```

#### 配置认证（如果需要）

```bash
# 在 pip.conf 中添加认证信息
cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = http://admin:<EMAIL>:8081/repository/pypi-group/simple/
trusted-host = nexus.example.com
EOF
```

#### 企业环境配置

```bash
# 创建企业级配置
sudo mkdir -p /etc/pip
sudo cat > /etc/pip/pip.conf << 'EOF'
[global]
index-url = http://nexus.example.com:8081/repository/pypi-group/simple/
trusted-host = nexus.example.com
timeout = 60

[install]
trusted-host = nexus.example.com
EOF
```

### 第七步：测试和验证

#### 测试包安装

```bash
# 安装公共包（通过 proxy 仓库）
pip install requests

# 安装内部包（从 hosted 仓库）
pip install your-internal-package

# 查看包信息
pip show requests

# 列出已安装的包
pip list
```

#### 验证仓库连通性

```bash
# 测试仓库访问
curl -I http://nexus.example.com:8081/repository/pypi-group/simple/

# 搜索包
pip search your-package-name

# 查看包的可用版本
pip index versions your-package-name
```

## 总结与最佳实践

### 部署总结

通过本文的详细指导，我们成功实现了：

✅ **完整的 Nexus3 部署**: 从环境准备到服务启动的全流程
✅ **Docker 私有仓库**: 支持镜像推送、拉取和管理
✅ **Nginx 反向代理**: 提供统一访问入口和负载均衡
✅ **版本升级方案**: 安全可靠的升级和回滚策略
✅ **多格式仓库支持**: Yum、Helm、PyPI 等多种包管理格式
✅ **企业级配置**: 适用于生产环境的安全和性能配置

### 最佳实践建议

#### 1. 安全性
- **访问控制**: 配置细粒度的用户权限和角色
- **网络安全**: 使用 HTTPS 和防火墙保护服务
- **数据加密**: 敏感数据传输和存储加密
- **定期审计**: 监控访问日志和异常行为

#### 2. 性能优化
- **资源配置**: 根据使用量调整 JVM 内存和 CPU 资源
- **存储优化**: 使用 SSD 存储提高 I/O 性能
- **网络优化**: 配置 CDN 和缓存策略
- **清理策略**: 定期清理过期和未使用的制品

#### 3. 运维管理
- **监控告警**: 配置系统监控和告警机制
- **备份策略**: 实施定期数据备份和恢复测试
- **文档维护**: 保持部署和配置文档的更新
- **团队培训**: 确保团队成员熟悉操作流程

#### 4. 扩展规划
- **高可用部署**: 考虑集群部署和故障转移
- **容量规划**: 根据业务增长规划存储和带宽
- **集成开发**: 与 CI/CD 流水线深度集成
- **多环境管理**: 为不同环境配置独立的仓库

### 常用维护命令

```bash
# 系统监控
docker stats nexus3
df -h /application/nexus3/data
netstat -tlnp | grep -E "8081|8082|8083"

# 日志查看
docker logs -f nexus3
tail -f /var/log/nginx/nexus.example.com.log

# 备份操作
tar -czf nexus3-backup-$(date +%Y%m%d).tar.gz /application/nexus3/data

# 清理操作
docker system prune -f
find /application/nexus3/data -name "*.tmp" -delete
```

### 技术支持资源

- **官方文档**: [Nexus Repository Manager 3](https://help.sonatype.com/repomanager3)
- **社区论坛**: [Sonatype Community](https://community.sonatype.com/)
- **GitHub 仓库**: [nexus-public](https://github.com/sonatype/nexus-public)
- **Docker Hub**: [sonatype/nexus3](https://hub.docker.com/r/sonatype/nexus3)

通过本指南，您已经掌握了 Nexus3 的完整部署和管理技能，可以为企业构建一个稳定、安全、高效的制品管理平台。在实际使用过程中，请根据具体需求调整配置参数，并持续关注官方更新和安全公告。
