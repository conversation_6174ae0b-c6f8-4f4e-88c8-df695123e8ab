---
title: "代码展示：苹果窗口风格代码框"
date: 2025-08-02T17:20:00+08:00
description: "展示新的苹果窗口风格代码框效果"
summary: "查看我们全新的苹果窗口风格代码框，支持多种编程语言和复制功能。"
tags: ["代码", "样式", "展示"]
categories: ["技术"]
draft: false
---

# 苹果窗口风格代码框展示

我们为博客添加了全新的苹果窗口风格代码框，具有以下特性：

## 特性

- 🍎 **苹果窗口风格**：仿照 macOS 窗口设计，包含红绿黄三色控制按钮
- 🎨 **主题适配**：完美支持浅色和深色模式
- 📋 **一键复制**：悬停显示复制按钮，支持键盘快捷键
- 🏷️ **语言标签**：自动识别并显示编程语言
- 📱 **响应式设计**：在各种设备上都有良好的显示效果
- ♿ **无障碍支持**：支持键盘导航和屏幕阅读器

## 代码示例

### JavaScript

```javascript
// 现代 JavaScript 示例
const fetchUserData = async (userId) => {
  try {
    const response = await fetch(`/api/users/${userId}`);
    const userData = await response.json();
    
    return {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      preferences: {
        theme: userData.theme || 'light',
        language: userData.language || 'zh-CN'
      }
    };
  } catch (error) {
    console.error('获取用户数据失败:', error);
    throw new Error('无法获取用户信息');
  }
};

// 使用示例
fetchUserData(123)
  .then(user => console.log('用户信息:', user))
  .catch(error => console.error('错误:', error));
```

### Python

```python
# Python 数据处理示例
import pandas as pd
import numpy as np
from typing import List, Dict, Optional

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, data_source: str):
        self.data_source = data_source
        self.data: Optional[pd.DataFrame] = None
    
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_source)
            print(f"成功加载 {len(self.data)} 行数据")
            return self.data
        except FileNotFoundError:
            raise ValueError(f"找不到数据文件: {self.data_source}")
    
    def clean_data(self) -> pd.DataFrame:
        """清理数据"""
        if self.data is None:
            raise ValueError("请先加载数据")
        
        # 删除重复行
        self.data = self.data.drop_duplicates()
        
        # 处理缺失值
        self.data = self.data.fillna(method='forward')
        
        return self.data
    
    def analyze(self) -> Dict[str, float]:
        """数据分析"""
        if self.data is None:
            raise ValueError("请先加载数据")
        
        return {
            'mean': self.data.select_dtypes(include=[np.number]).mean().to_dict(),
            'std': self.data.select_dtypes(include=[np.number]).std().to_dict(),
            'count': len(self.data)
        }

# 使用示例
processor = DataProcessor('data.csv')
processor.load_data()
processor.clean_data()
results = processor.analyze()
print("分析结果:", results)
```

### Go

```go
// Go 并发处理示例
package main

import (
    "context"
    "fmt"
    "log"
    "sync"
    "time"
)

// Worker 工作器接口
type Worker interface {
    Process(ctx context.Context, data interface{}) error
}

// TaskProcessor 任务处理器
type TaskProcessor struct {
    workers    []Worker
    maxWorkers int
    timeout    time.Duration
}

// NewTaskProcessor 创建新的任务处理器
func NewTaskProcessor(maxWorkers int, timeout time.Duration) *TaskProcessor {
    return &TaskProcessor{
        workers:    make([]Worker, 0, maxWorkers),
        maxWorkers: maxWorkers,
        timeout:    timeout,
    }
}

// ProcessTasks 并发处理任务
func (tp *TaskProcessor) ProcessTasks(ctx context.Context, tasks []interface{}) error {
    ctx, cancel := context.WithTimeout(ctx, tp.timeout)
    defer cancel()

    taskChan := make(chan interface{}, len(tasks))
    errorChan := make(chan error, len(tasks))
    
    // 启动工作协程
    var wg sync.WaitGroup
    for i := 0; i < tp.maxWorkers; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            for task := range taskChan {
                select {
                case <-ctx.Done():
                    errorChan <- ctx.Err()
                    return
                default:
                    if err := tp.processTask(ctx, task); err != nil {
                        errorChan <- fmt.Errorf("worker %d: %w", workerID, err)
                    }
                }
            }
        }(i)
    }

    // 发送任务
    go func() {
        defer close(taskChan)
        for _, task := range tasks {
            select {
            case taskChan <- task:
            case <-ctx.Done():
                return
            }
        }
    }()

    // 等待完成
    go func() {
        wg.Wait()
        close(errorChan)
    }()

    // 收集错误
    for err := range errorChan {
        if err != nil {
            return err
        }
    }

    return nil
}

func (tp *TaskProcessor) processTask(ctx context.Context, task interface{}) error {
    // 模拟任务处理
    select {
    case <-time.After(100 * time.Millisecond):
        fmt.Printf("处理任务: %v\n", task)
        return nil
    case <-ctx.Done():
        return ctx.Err()
    }
}

func main() {
    processor := NewTaskProcessor(5, 10*time.Second)
    tasks := []interface{}{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
    
    ctx := context.Background()
    if err := processor.ProcessTasks(ctx, tasks); err != nil {
        log.Fatalf("处理任务失败: %v", err)
    }
    
    fmt.Println("所有任务处理完成")
}
```

### Rust

```rust
// Rust 异步编程示例
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: u64,
    pub name: String,
    pub email: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone)]
pub struct UserCache {
    cache: Arc<RwLock<HashMap<u64, User>>>,
    ttl: std::time::Duration,
}

impl UserCache {
    pub fn new(ttl: std::time::Duration) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            ttl,
        }
    }

    pub async fn get(&self, user_id: u64) -> Option<User> {
        let cache = self.cache.read().await;
        cache.get(&user_id).cloned()
    }

    pub async fn set(&self, user: User) {
        let mut cache = self.cache.write().await;
        cache.insert(user.id, user);
    }

    pub async fn remove(&self, user_id: u64) -> Option<User> {
        let mut cache = self.cache.write().await;
        cache.remove(&user_id)
    }

    pub async fn clear(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
    }

    pub async fn size(&self) -> usize {
        let cache = self.cache.read().await;
        cache.len()
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let cache = UserCache::new(std::time::Duration::from_secs(3600));

    // 创建测试用户
    let user = User {
        id: 1,
        name: "张三".to_string(),
        email: "<EMAIL>".to_string(),
        created_at: chrono::Utc::now(),
    };

    // 缓存用户
    cache.set(user.clone()).await;
    println!("用户已缓存: {:?}", user);

    // 获取用户
    if let Some(cached_user) = cache.get(1).await {
        println!("从缓存获取用户: {:?}", cached_user);
    }

    // 缓存统计
    println!("缓存大小: {}", cache.size().await);

    Ok(())
}
```

### Shell/Bash

```bash
#!/bin/bash

# 自动化部署脚本
set -euo pipefail

# 配置变量
PROJECT_NAME="my-blog"
BUILD_DIR="public"
DEPLOY_DIR="/var/www/html"
BACKUP_DIR="/var/backups/website"
LOG_FILE="/var/log/deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    command -v hugo >/dev/null 2>&1 || error "Hugo 未安装"
    command -v rsync >/dev/null 2>&1 || error "rsync 未安装"
    
    log "依赖检查完成"
}

# 备份现有网站
backup_website() {
    log "备份现有网站..."
    
    if [ -d "$DEPLOY_DIR" ]; then
        local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        if rsync -av "$DEPLOY_DIR/" "$BACKUP_DIR/$backup_name/"; then
            log "备份完成: $BACKUP_DIR/$backup_name"
        else
            error "备份失败"
        fi
    else
        warn "部署目录不存在，跳过备份"
    fi
}

# 构建网站
build_website() {
    log "构建网站..."
    
    # 清理旧的构建文件
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    # 构建网站
    if hugo --gc --minify; then
        log "网站构建完成"
    else
        error "网站构建失败"
    fi
    
    # 验证构建结果
    if [ ! -d "$BUILD_DIR" ] || [ -z "$(ls -A $BUILD_DIR)" ]; then
        error "构建目录为空"
    fi
}

# 部署网站
deploy_website() {
    log "部署网站..."
    
    # 创建部署目录
    sudo mkdir -p "$DEPLOY_DIR"
    
    # 同步文件
    if sudo rsync -av --delete "$BUILD_DIR/" "$DEPLOY_DIR/"; then
        log "网站部署完成"
    else
        error "网站部署失败"
    fi
    
    # 设置权限
    sudo chown -R www-data:www-data "$DEPLOY_DIR"
    sudo chmod -R 755 "$DEPLOY_DIR"
}

# 验证部署
verify_deployment() {
    log "验证部署..."
    
    # 检查关键文件
    local key_files=("index.html" "sitemap.xml")
    
    for file in "${key_files[@]}"; do
        if [ ! -f "$DEPLOY_DIR/$file" ]; then
            error "关键文件缺失: $file"
        fi
    done
    
    # 检查网站可访问性
    if command -v curl >/dev/null 2>&1; then
        if curl -f -s http://localhost/ >/dev/null; then
            log "网站可访问性验证通过"
        else
            warn "网站可访问性验证失败"
        fi
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份..."
    
    if [ -d "$BACKUP_DIR" ]; then
        # 保留最近7天的备份
        find "$BACKUP_DIR" -type d -name "backup-*" -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
        log "旧备份清理完成"
    fi
}

# 主函数
main() {
    log "开始部署 $PROJECT_NAME..."
    
    check_dependencies
    backup_website
    build_website
    deploy_website
    verify_deployment
    cleanup_old_backups
    
    log "部署完成！"
}

# 错误处理
trap 'error "部署过程中发生错误"' ERR

# 执行主函数
main "$@"
```

## 使用说明

### 复制功能

- **鼠标操作**：将鼠标悬停在代码块上，会显示复制按钮
- **键盘快捷键**：聚焦代码块后按 `Ctrl+Shift+C`（Windows/Linux）或 `Cmd+Shift+C`（macOS）

### 语言支持

目前支持以下编程语言的自动识别和标签显示：

- JavaScript/TypeScript
- Python
- Go
- Rust
- Bash/Shell
- HTML/CSS/SCSS
- JSON/YAML/TOML
- SQL
- Dockerfile
- Markdown

### 主题适配

代码框会自动适配网站的主题模式：

- **浅色模式**：白色背景，深色文字
- **深色模式**：深色背景，浅色文字

### 响应式设计

- **桌面设备**：完整的苹果窗口效果
- **平板设备**：适中的字体大小和间距
- **移动设备**：优化的布局和控制按钮大小

## 技术实现

这个苹果窗口风格代码框使用了以下技术：

- **CSS3**：渐变、阴影、动画效果
- **JavaScript**：复制功能、键盘支持
- **Hugo**：模板集成和资源处理
- **响应式设计**：媒体查询和弹性布局

享受全新的代码阅读体验吧！ 🎉
