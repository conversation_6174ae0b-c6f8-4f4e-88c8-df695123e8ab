---
title: "K3s 集群修改 Flannel CNI 插件网络模式"
date: 2021-07-29T08:56:04+08:00
draft: false
tags: [ "flannel", "n1", "linux"]
tags_weight: 80
categories: ["k3s","openwrt","network"]
categories_weight: 80
keywords:
- k3s
- openwrt
- network
- flannel
- n1
- linux
- 模式更改
description: "k3s 集群修改 flannel 默认 vxlan 模式，更改为 host-gw 模式的记录"
---



# 背景说明

>在 [之前搭建](https://www.treesir.pub/post/n1-openwrt-k3s-deploy/) 的 k3s 集群中因为某些原因我将 openwrt 节点，进行了系统重装，更改固件为了 `esir` 高大全的 op 固件，由于其 固件中没有将 `vxlan` 模块编译进内核当中，而 k3s `默认` 使用的 cni 为 `flannel 的 vxlan` 模式，导致在初始化节点的时候会出现错误，导致节点添加不成功，我们知道原生 flannel 支持的模式不只单单只有 vxlan，还支持 **host-gw**、**udp** 模式。进行查阅 k3s 相关资料，看到 k3s 是支持切换多种网络模式的，于是决定将 flannel 模式更改为 `host-gw` 。



## 节点说明

| IP 地址       | 节点名称 | **机型**        | **配置**            | **操作系统**                    | **节点角色**  |
| ------------- | -------- | --------------- | ------------------- | ------------------------------- | ------------- |
| ***********   | openwrt  | 占美 (机型不详) | 4c 2g ( cpu N2940 ) | openWrt（X86_64 `esir 高大全`） | node/agent    |
| ************* | n1       | 斐讯 N1         | 4c 2g               | Armbian ( 5.0.2-aml-s905)       | master/server |
| ***********13 | n2       | 斐讯 N1         | 4c 2g               | Armbian ( 5.0.2-aml-s905)       | node/agent    |
| ***********14 | n3       | 斐讯 N1         | 4c 2g               | Armbian ( 5.0.2-aml-s905)       | node/agent    |



# 更改模式为 `host-gw`

> 由于我目前的环境，n1、n2、n3 已组建为了 k3s 集群，使用的网络模式为 默认模式 `vxlan` ，这里需要进行更改即可。而 `openwrt` 这个节点是需要加入的节点，打算将其加入为 `agent` 节点。组建的集群规模为: `一主三从` 。



## 原规模 集群更改模式

> 这里指 `n1、n2、n3 ` 这三台节点。下面步骤分别在各节点，进行相同操作进行更改。

```bash
vim `systemctl status k3s 2>&1|grep loaded|awk -F '[(;]' '{print $2}'` # 编辑 k3s 配置文件, 加入下述配置
...
        '--flannel-backend' \
        'host-gw'
...
```

![image-20210729100350622](https://cdn.treesir.pub/img/image-20210729100350622.png)

### 重启生效

```bash
systemctl daemon-reload \
&& systemctl restart k3s.service
```

### 检查是否生效

```bash
apt install sshpass

for i in `seq 1 3`;do sshpass -p 'xxxx' ssh -t -o StrictHostKeyChecking=no n"${i}" 'cat /var/lib/rancher/k3s/agent/etc/flannel/net-conf.json';done
```

![image-20210729101620888](https://cdn.treesir.pub/img/image-20210729101620888.png)

> 可以看到，即 `/var/lib/rancher/k3s/agent/etc/flannel/net-conf.json` 这个文件中，显示 type 为 `host-gw` 即 可以

![image-20210729101815742](https://cdn.treesir.pub/img/image-20210729101815742.png)

> 跨集群通讯也是没有问题的。



# 配置加入节点

> 原集群测试没有问题后，尝试对 openwrt 节点进行纳入集群中，进行管理。

## 测试启动

> 这里 openwrt 安装 k3s 的步骤，部分省略，可以参考我早期整理的 [文档](https://www.treesir.pub/post/n1-openwrt-k3s-deploy/#%E4%B8%8B%E8%BD%BD%E5%AE%89%E8%A3%85%E5%8C%85)

```bash
k3s agent --server https://*************:6443 --token K106ccb265579f1e40038844787c9ce4dd8528b6c58e26894e953661c9a907ef5d2::server:4843c59507b6840cb1c3bea454570f85 --docker --kube-apiserver-arg service-node-port-range=40000-65000 --no-deploy traefik --write-kubeconfig ~/.kube/config --write-kubeconfig-mode 666
```

![image-20210729102428410](https://cdn.treesir.pub/img/image-20210729102428410.png)

> 测试启动后，在 `master` 节点中查看，相关节点是否就绪。可以看到我这里是没有问题的。



## 配置 k3s 为服务，并设置开机自启

**相关配置文件展示**

```bash
cat /etc/config/k3s 
config globals 'globals'
        option opts '--server https://*************:6443 --flannel-backend host-gw --token K106ccb265579f1e40038844787c9ce4dd8528b6c58e26894e953661c9a907ef5d2::server:4843c59507b6840cb1c3bea454570f85 --docker --kube-apiserver-arg service-node-port-range=40000-65000 --no-deploy traefik --write-kubeconfig ~/.kube/config --write-kubeconfig-mode 666'
        option root '/application/k3s'
        

cat /etc/init.d/k3s 
#!/bin/sh /etc/rc.common

START=60
STOP=20

PIDFILE=/var/run/k3s.pid
EXEC="/usr/bin/k3s"

ensure_cgroup_mount() {
  # Unmount /sys/fs/cgroup if mounted as cgroup
  grep -q ' /sys/fs/cgroup cgroup' /proc/self/mounts && umount /sys/fs/cgroup

  grep -q ' /sys/fs/cgroup tmpfs' /proc/self/mounts \
    || mount -t tmpfs -o uid=0,gid=0,mode=0755 cgroup /sys/fs/cgroup

  for sys in $(awk '!/^#/ { if ($4 == 1) print $1 }' /proc/cgroups); do
    mnt="/sys/fs/cgroup/$sys"
    grep -q "cgroup $mnt " /proc/self/mounts && continue
    mkdir -p "$mnt"
    mount -n -t cgroup -o $sys cgroup "$mnt"
  done
}

start() {
  ensure_cgroup_mount
  start-stop-daemon -S -b -x "$EXEC" -m -p "$PIDFILE" \
    -- agent $(uci get k3s.globals.opts) \
    --data-dir $(uci get k3s.globals.root)
}

stop() {
  start-stop-daemon -K -p "$PIDFILE"
```

**设置开机自启**

```bash
chmod a+x /etc/init.d/k3s 

/etc/init.d/k3s start \
&& /etc/init.d/k3s enable
```



## 防火墙策略开放

> 由于 openwrt 中对，网络进行的 策略管理，这里我们要想 跨节点与 openwrt 中的 pod 进行通讯时，还需要将对应的 `接口` 进行开放一下。步骤实现方式在之前 整理 的 [文档当中](https://www.treesir.pub/post/n1-openwrt-k3s-deploy/#openwrt-%E5%BC%80%E6%94%BE%E9%98%B2%E7%81%AB%E5%A2%99)。



## 测试 openwrt pod 网络通讯

> 这里已我目前集群中的 `blog` 服务为示例进行测试。使用 dashboard 为 `lens 5.x`。

删除 pod ，触发 pod 重新调度，使其调度到 openwrt 节点当中。

![image-20210729103444297](https://cdn.treesir.pub/img/image-20210729103444297.png)



在 n1 节点当中，测试访问 openwrt 节点 pod，查看 pod 之间网络是否通畅。

![image-20210729103732183](https://cdn.treesir.pub/img/image-20210729103732183.png)

> 访问对应的 pod ip，正常返回 http 状态码 `200`,  即表示网络通畅且正常，此次集群升级结束。

# 参考文档

https://rancher.com/docs/k3s/latest/en/installation/install-options/server-config/#networking

# 总结

> 由于我这里在家里面搭的是一套 `娱乐` 性集群，部署的服务也是一些轻量级的应用，对性能什么的要求不高。 如果是生产环境的话，可能对相关的性能表现是比较在意，默认的 k3s `kube-porxy` 的模式是 使用的 `iptables`，一般生产是建议使用 `ipvs` 性能好一下，如有需求可以参加此篇 [文档](https://github.com/k3s-io/k3s/tree/master/vendor/k8s.io/kubernetes/pkg/proxy/ipvs) 进行实现。
