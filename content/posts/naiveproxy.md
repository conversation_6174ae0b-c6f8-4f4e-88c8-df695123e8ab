---
title: "NaiveProxy"
date: 2022-12-11T15:47:03+08:00
draft: false
tags: [ "Naiveproxy"]
tags_weight: 60
categories: ["proxy"]
categories_weight: 30
keywords:
- Naivep
- proxy
description: "NaiveProxy 部署使用"
---

## 说明

> `懂的自然懂`，直接上文档。

1. Caddy 编译安装

   ```bash
   wget https://go.dev/dl/go1.19.linux-arm64.tar.gz
   
   tar -zxvf go1.19.linux-arm64.tar.gz -C /usr/local/
   
   
   cat >> /etc/profile << EOF
   GO_HOME=/usr/local/go
   export PATH=\$PATH:\$GO_HOME/bin/:/root/go/bin
   EOF
   
   source /etc/profile
   
   
   go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest
   
   
   xcaddy build --with github.com/caddyserver/forwardproxy@caddy2=github.com/klzgrad/forwardproxy@naive
   
   mv caddy /usr/local/bin/caddy
   
   mkdir /etc/caddy
   ```

2. Caddyfile 配置文件创建

   > 这里需要一个 Tls 证书，自己结合实际情况配置更改一下。

   ```bash
   cat > /etc/caddy/Caddyfile << EOF
   {
       debug
       #http_port    28080
       https_port   2443
       #default_bind  $(hostname -a).treesir.pub
       auto_https off
       servers :2443 {
         listener_wrappers {
           http_redirect
           tls
         }
       metrics
       max_header_size 4096mb
       protocols h1 h2 h2c h3
       }
   }
   
   :2443, $(hostname -a).treesir.pub
   tls /data/traefik-v2ray/acme-cert/cert/treesir.pub.pem /data/traefik-v2ray/acme-cert/cert/treesir.pub.key
   route {
     forward_proxy {
       basic_auth xxxx fKQ3rdKae7YgzCn3
       hide_ip
       hide_via
       probe_resistance www.bing.com
     }
     reverse_proxy  https://www.bing.com
     #file_server {
     #  root /data/wwwroot/default
     #}
   }
   EOF
   ```

3. Systemctl 配置文件创建，并设置开机自启动

   ```bash
   cat > /etc/systemd/system/naive.service << EOF
   [Unit]
   Description=Caddy
   Documentation=https://caddyserver.com/docs/
   After=network.target network-online.target
   Requires=network-online.target
   
   [Service]
   Type=notify
   User=root
   Group=root
   ExecStart=/usr/local/bin/caddy run --environ --config /etc/caddy/Caddyfile
   ExecReload=/usr/local/bin/caddy reload --config /etc/caddy/Caddyfile
   TimeoutStopSec=5s
   LimitNOFILE=1048576
   LimitNPROC=512
   PrivateTmp=true
   ProtectSystem=full
   AmbientCapabilities=CAP_NET_BIND_SERVICE
   
   [Install]
   WantedBy=multi-user.target
   EOF
   
   systemctl enable naive.service --now  # 启动和设置开机自启动
   ```

   

##  解锁查询

```bash
# `amd 架构`
wget -O /usr/local/bin/nf https://github.com/sjlleo/netflix-verify/releases/download/v3.1.0/nf_linux_amd64 && chmod a+x /usr/local/bin/nf && nf

# `arm 架构`
wget -O /usr/local/bin/nf https://github.com/sjlleo/netflix-verify/releases/download/v3.1.0-1/nf_linux_arm64 && chmod a+x /usr/local/bin/nf && nf
```



## 其他

1. 关闭 IPV6

   ```bash
   echo "1" > /proc/sys/net/ipv6/conf/all/disable_ipv6 
   ```

