---
title: "使用 Docker Compose 部署 OpenConnect VPN 服务器"
date: 2021-05-15T16:20:46+08:00
draft: false
tags: [ "openconnect", "docker-compose", "openldap"]
tags_weight: 20
categories: ["linux", "network"]
categories_weight: 20
keywords:
- openconnect
- docker-compose
- 一键部署
- openldap
- docker
description: "基于 Docker Compose 快速部署 OpenConnect VPN 服务器的完整指南"
---



# 环境要求

本文档基于以下环境进行部署：

- **Docker Compose**：1.18.0
- **操作系统**：OpenWrt (x86)
- **Docker 版本**：19.03.12
- **Docker 镜像**：`yangzun/docker-openconnect-ldap:latest`

> **说明**：该镜像基于 `morganonbass/ocserv-ldap` 进行了修改和优化，解决了原镜像无法正常启动的问题。





# 部署准备

## 安装 Docker Compose

```bash
yum install -y docker-compose
```



# 配置部署文件

## 创建项目目录

```bash
mkdir -p /data/docker-compose/openConnect
cd /data/docker-compose/openConnect
```

## 创建 Docker Compose 配置

```bash
cat > docker-compose.yaml << EOF
version: "3"
services:
  ocserv:
    container_name: ocserv
    image: yangzun/docker-openconnect-ldap:latest
    ports:
      - "1443:443/tcp"
      - "1443:443/udp"
    environment:
      LISTEN_PORT: 443
      TUNNEL_MODE: 'split-include'
      TUNNEL_ROUTES: '192.168.8.0/24'
      DNS_SERVERS: 192.168.8.1
      CLIENTNET: *************
      CLIENTNETMASK: 255.255.255.128
      BASEDN: 'dc=treesir,dc=pub'
      LDAPURI: 'ldap://192.168.8.1:389/'
      BINDDN: 'cn=admin,dc=treesir,dc=pub'
      BINDPW: '123456'
      SEARCHSCOPE: 'ou=users,dc=treesir,dc=pub'
      PAM_LOGIN_ATTRIBUTE: 'uid'
      CA_CN: 'VPN CA'
      CA_ORG: 'OCSERV'
      CA_DAYS: 9999 
      SRV_CN: 'nps.treesir.pub'
      SRV_ORG: 'Example Company'
      SRV_DAYS: 9999
    volumes:
      - './config/:/config/'
    cap_add:
      - NET_ADMIN
    privileged: true
    restart: unless-stopped
EOF
```

> **配置说明**：环境变量的详细说明请参考 [Docker Hub 页面](https://hub.docker.com/r/yangzun/docker-openconnect-ldap)。OpenLDAP 的部署配置请参考 [LDAP 部署文档](https://www.treesir.pub/post/docker-deploy-ldap)。

## 启动服务

```bash
docker-compose up -d
```

# 连接测试

我们使用 Cisco AnyConnect 客户端进行连接测试。

> **客户端下载**：[Cisco AnyConnect 官方下载页面](https://www.cisco.com/c/en/us/support/security/anyconnect-secure-mobility-client-v4-x/model.html)

## 内网连接测试

![image-20210515164132639](https://cdn.treesir.pub/img/image-20210515164132639.png)

测试结果显示，使用内网地址可以正常连接，客户端获取到的 IP 地址为配置文件中指定的地址段 `*************/25`。



## 公网连接测试

由于运营商未提供公网 IP，这里使用内网穿透工具 NPS 进行测试演示。

### NPS 端口映射配置

![image-20210515165420780](https://cdn.treesir.pub/img/image-20210515165420780.png)

> **协议选择说明**：这里使用 TCP 协议进行映射。相比 UDP 协议，TCP 具有更好的稳定性，不易被运营商拦截。在网络攻击等异常情况下，运营商通常会优先关闭 UDP 等不安全协议。虽然 UDP 在传输速度上有一定优势，但 TCP 的稳定性更适合 VPN 连接。



### PC 客户端测试

![image-20210515170037710](https://cdn.treesir.pub/img/image-20210515170037710.png)

PC 端连接测试成功，可以正常建立 VPN 连接。

![image-20210516102945530](https://cdn.treesir.pub/img/image-20210516102945530.png)

断开连接后，服务器端会输出相应的日志信息。

### 移动端测试

使用 AnyConnect 移动端客户端进行测试：

![image-20210516103818697](https://cdn.treesir.pub/img/image-20210516103818697.png)


# 常见问题

## iptables 规则报错

**问题现象**：

![image-20210515225610391](https://cdn.treesir.pub/img/image-20210515225610391.png)

![image-20210515225706525](https://cdn.treesir.pub/img/image-20210515225706525.png)

**解决方案**：

该问题由 iptables 版本不兼容导致，需要切换到 legacy 版本：

```bash
update-alternatives --set iptables /usr/sbin/iptables-legacy
```

> **说明**：该修复已集成到 Dockerfile 的 `docker-entrypoint.sh` 脚本中，代码提交后会自动触发镜像构建。

![image-20210516104420599](https://cdn.treesir.pub/img/image-20210516104420599.png)

# 总结

## OpenConnect vs OpenVPN

OpenConnect 相比 OpenVPN 具有以下优势：

- **集中化路由管理**：路由配置可在服务端统一控制，无需修改客户端配置文件
- **安全性更高**：避免了客户端配置文件的安全风险，便于管理员统一管控
- **性能表现**：两者都基于 TLS 证书验证，性能差异不大
- **部署便捷**：通过 Docker Compose 可实现一键部署

## 参考文档

- [OCServ 官方文档](https://ocserv.gitlab.io/www/manual.html)
