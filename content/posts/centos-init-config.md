---
title: "CentOS 7 系统初始化配置完整指南"
date: 2020-12-21T09:34:01+08:00
draft: false
tags: [ "centos7","linux","系统优化"]
tags_weight: 80
categories: ["linux","centos"]
categories_weight: 80
keywords:
- centos7
- 系统优化
- 初始化配置
- 服务器部署
- 性能调优
description: "CentOS 7 系统安装完成后的必要初始化配置和性能优化指南，适用于生产环境部署"
---

新安装的 CentOS 7 系统需要进行一系列初始化配置和优化，以确保系统的安全性、稳定性和性能。本文提供了一套完整的初始化配置方案，特别适用于容器化环境和 Kubernetes 集群节点。

> **⚠️ 重要提示**: 本文中的配置已在 CentOS 7 环境中验证可行，其他 Linux 发行版请根据实际情况调整

## 系统性能优化

### 内核参数调优

内核参数优化是系统性能调优的重要环节，以下配置特别适用于 Kubernetes 节点和 etcd 集群：

> **配置说明**: 此配置参考了 [Rancher 官方最佳实践](https://docs.rancher.cn/docs/rancher2/best-practices/optimize/os/_index)，针对容器化环境进行了优化
创建内核参数配置文件：

```bash
# 创建内核优化配置文件
cat > /etc/sysctl.d/99-kubernetes.conf << 'EOF'
# ===== 网络配置 =====
# 启用桥接网络的 iptables 处理（Kubernetes 必需）
net.bridge.bridge-nf-call-ip6tables=1
net.bridge.bridge-nf-call-iptables=1

# 启用 IP 转发（容器网络必需）
net.ipv4.ip_forward=1
net.ipv4.conf.all.forwarding=1

# 邻居表优化（提高网络性能）
net.ipv4.neigh.default.gc_thresh1=4096
net.ipv4.neigh.default.gc_thresh2=6144
net.ipv4.neigh.default.gc_thresh3=8192
net.ipv4.neigh.default.gc_interval=60
net.ipv4.neigh.default.gc_stale_time=120

# TCP 网络优化
net.ipv4.tcp_slow_start_after_idle=0
net.ipv4.tcp_max_syn_backlog=8096
net.ipv4.tcp_wmem=4096 12582912 16777216
net.ipv4.tcp_rmem=4096 12582912 16777216

# 网络缓冲区优化
net.core.rmem_max=16777216
net.core.wmem_max=16777216
net.core.netdev_max_backlog=16384
net.core.somaxconn=32768

# ===== 文件系统配置 =====
# 文件监控优化（适用于大量文件监控的应用）
fs.inotify.max_user_watches=524288
fs.inotify.max_user_instances=8192
fs.inotify.max_queued_events=16384

# 文件句柄限制
fs.file-max=2097152

# 内存映射区域限制（Elasticsearch 等应用需要）
vm.max_map_count=262144

# 允许分离挂载点（容器环境需要）
fs.may_detach_mounts=1

# ===== 安全配置 =====
# 硬链接和软链接保护
fs.protected_hardlinks=1
fs.protected_symlinks=1

# 禁用源路由（安全考虑）
net.ipv4.conf.default.accept_source_route=0
net.ipv4.conf.all.accept_source_route=0

# 地址提升配置
net.ipv4.conf.default.promote_secondaries=1
net.ipv4.conf.all.promote_secondaries=1

# ===== 系统性能配置 =====
# 禁用 swap（Kubernetes 推荐）
vm.swappiness=0

# 内核监控配置
kernel.softlockup_all_cpu_backtrace=1
kernel.softlockup_panic=0
kernel.watchdog_thresh=30

# 性能监控配置（Prometheus Node Exporter 需要）
kernel.perf_event_paranoid=-1

# 调试配置
kernel.yama.ptrace_scope=0
kernel.core_uses_pid=1

# ===== IPv6 配置 =====
# 禁用 IPv6（如果不需要的话）
net.ipv6.conf.all.disable_ipv6=1
net.ipv6.conf.default.disable_ipv6=1
net.ipv6.conf.lo.disable_ipv6=1
EOF
net.ipv4.conf.all.rp_filter=0
net.ipv4.conf.default.rp_filter=0
net.ipv4.conf.default.arp_announce = 2
net.ipv4.conf.lo.arp_announce=2
net.ipv4.conf.all.arp_announce=2

# see details in https://help.aliyun.com/knowledge_detail/41334.html
net.ipv4.tcp_max_tw_buckets=5000
net.ipv4.tcp_syncookies=1
net.ipv4.tcp_fin_timeout=30
net.ipv4.tcp_synack_retries=2
kernel.sysrq=1

# tcp 连接优化
net.ipv4.tcp_tw_reuse=1
net.ipv4.tcp_tw_recycle=1
" >> /etc/sysctl.conf
sysctl -p # 使其生效
```
## 更新系统内核
  > 脚本中默认安装lt最新稳定版本的内核，如需更改为ml内核修改main函数中的"VERSION_TYPE"变量即可
  ```bash
    # 使用脚本如下所示
    cat update_kernel.sh
    #!/bin/bash

    ##########################################################
    # Author        : LeafyJohn
    # Email         : <EMAIL>
    # Last modified : 2020-12-21 10:38:27
    # Description   : Centos update linux kernel
    # License: Attribution-NonCommercial 4.0 International
    ###########################################################
    
    update_kernel(){
        echo "当前内核是:" `grub2-editenv list|awk -F "=" '{print $2}'`
        curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo
        yum -y install epel-release curl wget
        sed -i "0,/enabled=0/s//enabled=1/" /etc/yum.repos.d/epel.repo
        yum remove -y kernel-devel
        rpm --import https://www.elrepo.org/RPM-GPG-KEY-elrepo.org
        rpm -Uvh http://www.elrepo.org/elrepo-release-7.0-2.el7.elrepo.noarch.rpm
        yum --disablerepo="*" --enablerepo="elrepo-kernel" list available |tee kernel_list
        KERNEL_VERSION=`cat kernel_list |grep "${VERSION_TYPE}" |tail -1|awk '{print $2}'`
        echo "更新系统内核版本为: ${KERNEL_VERSION}"
        yum -y --enablerepo=elrepo-kernel install kernel-"${VERSION_TYPE}"
        grub2-set-default "CentOS Linux (${KERNEL_VERSION}.x86_64) 7 (Core)"
        wget https://elrepo.org/linux/kernel/el7/x86_64/RPMS/kernel-"${VERSION_TYPE}"-devel-"${KERNEL_VERSION}".x86_64.rpm
        rpm -ivh kernel-"${VERSION_TYPE}"-devel-"${KERNEL_VERSION}".x86_64.rpm
        yum -y --enablerepo=elrepo-kernel install kernel-"${VERSION_TYPE}"-devel
        echo "当前内核是:" `grub2-editenv list|awk -F "=" '{print $2}'`
        read -p "更新内核需要重启系统，是否现在重启 ? [Y/n] :" yn
            [ -z "${yn}" ] && yn="y"
            if [[ $yn == [Yy] ]]; then
                    echo -e "系统 重启中..."
                    reboot
            fi
    }
    
    main(){
        # install "lt" or "ml"
        VERSION_TYPE='lt'
        update_kernel
    }
    main
  ```
  > ⚠️ 内核安装重启完成后我们还需要对他进行版本锁定，防止在执行yum update更新软件包的时候更新了内核，将我们刚才设置的内核给覆盖掉。
  > ```bash
  > yum -y install yum-versionlock \
  > && yum versionlock add kernel* \
  > && yum versionlock list
  > ```

## 系统文件句柄优化
  ```bash
  cat >> /etc/security/limits.conf <<EOF
  * soft nofile 65535
  * hard nofile 65536
  hive   - nofile 65535
  hive   - nproc  65535
  EOF
  
  sed -i 's#4096#65535#g'   /etc/security/limits.d/20-nproc.conf  # 加大普通用户限制也可以改为 unlimited
  egrep -v "^$|^#" /etc/security/limits.d/20-nproc.conf  # 检查是否生效
  ```

## 统一网卡名称为 ethx
```bash
# 统一网卡名称为ethx
sudo sed -i 's/GRUB_CMDLINE_LINUX="\(.*\)"/GRUB_CMDLINE_LINUX="net.ifnames=0 cgroup_enable=memory swapaccount=1 biosdevname=0 \1"/g' /etc/default/grub;
sudo grub2-mkconfig -o /boot/grub2/grub.cfg
```

## 关闭 Selinux
```bash
sed -i 's#SELINUX=enforcing#SELINUX=disabled#g' /etc/selinux/config
grep -i  ^selinux= /etc/selinux/config 
setenforce 0
getenforce
```

# 常用软件安装配置及优化
## 配置yum源为 [aliyun](https://developer.aliyun.com/mirror/) 的国内源
```bash
mv /etc/yum.repos.d{,.bak} \
&& mkdir -p /etc/yum.repos.d \
&& curl -o /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-7.repo \
&& wget -O /etc/yum.repos.d/epel.repo http://mirrors.aliyun.com/repo/epel-7.repo \
&& yum clean all \
&& yum makecache fast
```

## 常用工具及依赖库的安装 (可选)
```bash
yum -y install telnet vim ntp ntpdate wget vim* net-tools iptables iptables-services git vim gcc glibc-static telnet bridge-utils net-tools wget telnet lrzsz tree ntsysv bash-completion gcc cmake bzip2-devel curl-devel db4-devel libjpeg-devel libpng-devel freetype-devel libXpm-devel gmp-devel libc-client-devel openldap-devel unixODBC-devel postgresql-devel sqlite-devel aspell-devel net-snmp-devel libxslt-devel libxml2-devel pcre-devel mysql-devel pspell-devel libmemcached libmemcached-devel zlib-devel bash-completion
```
## Openssh 优化
```bash
sed -i 's/#UseDNS yes/UseDNS no/' /etc/ssh/sshd_config # 禁用 dns 解析
sed -i '/^GSS/s/yes/no/g' /etc/ssh/sshd_config  # 禁用GSSAPI认证加快登录速度
systemctl restart sshd 
```

## 关闭防火墙
```bash
systemctl stop firewalld.service
systemctl disable firewalld.service
systemctl list-unit-files |grep firewalld
```

## 添加终端自动断开
```bash
# 为了增强系统的安全性 添加终端在用户输入空闲一段时间后自动断开 此示例为: 30分钟
echo """export TMOUT=1800
readonly TMOUT""" >> /etc/profile
source /etc/profile # 使设置立即生效
```

## 更新软件及系统版本
```bash
yum update \
&& yum upgrade
```

## Docker安装
```bash
sudo yum install -y yum-utils device-mapper-persistent-data lvm2 \
&& sudo yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo \
&& sudo yum makecache fast

# 安装前先检查一下需要安装的版本
yum list docker-ce.x86_64 --showduplicates | sort -r
docker-ce.x86_64            3:18.09.9-3.el7                    docker-ce-stable

# 安装指定的版本 (去掉第二列的 "3:"即可)
yum -y install docker-ce-18.09.9-3.el7
```
### 优化

- 配置文件优化
    ```bash
    systemctl stop docker.service
    sudo ip link del docker0
    mkdir -p /etc/docker
    touch /etc/docker/daemon.json
    cat > /etc/docker/daemon.json << EOF
    {
        "oom-score-adjust": -1000,
        "log-driver": "json-file",
        "log-opts": {
        "max-size": "100m",
        "max-file": "3"
        },
        "max-concurrent-downloads": 10,
        "exec-opts": ["native.cgroupdriver=systemd"],
        "max-concurrent-uploads": 10,
        "insecure-registries": ["idocker.io"],
        "registry-mirrors": ["https://7bezldxe.mirror.aliyuncs.com"],
        "storage-driver": "overlay2",
        "storage-opts": [
        "overlay2.override_kernel_check=true"
        ]
    }
    EOF

    systemctl daemon-reload \
    && systemctl restart docker  # 配置完成后重启加载一下配置文件 

    systemctl status docker # 注意检查一下是否正常启动
    ```
    > 常用项说明:
    > - "log-driver": "json-file" 设置json 格式日志
    > - "oom-score-adjust": -1000 防止容器被 内核 oom
    > - "log-opts" 设置容器日志大小
    > - "max-concurrent-downloads": 10 并行下载容器数量
    > - "max-concurrent-uploads": 10 并行上传
    > - "storage-driver": "overlay2" 设置存储驱动为 overlay2
    > - "bip" 容器默认的网段
    > - "registry-mirrors" 配置镜像下载加速这里使用的是阿里云的 (⚠️ 在离线部署时 此选项可以选择去掉)
    > - "insecure-registries" 信任的私服地址
- 服务进程优化
  ```bash
  sed -i '/\[Service\]/a\OOMScoreAdjust=-1000' /usr/lib/systemd/system/docker.service
  sed -i '/ExecReload/a\ExecStartPost=/usr/sbin/iptables -P FORWARD ACCEPT' /usr/lib/systemd/system/docker.service
  systemctl daemon-reload && systemctl restart docker
  ```
- 设置开机自启动
  ```bash
  systemctl daemon-reload \
  && systemctl restart docker \
  && systemctl enable docker.service
  ```
