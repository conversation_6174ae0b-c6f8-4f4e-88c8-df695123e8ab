---
title: "企业级 CentOS 7 系统初始化与优化完整指南"
date: 2020-12-21T09:34:01+08:00
draft: false
tags: [ "centos7", "linux", "system-optimization", "server-hardening", "production-deployment"]
tags_weight: 80
categories: ["linux","centos"]
categories_weight: 80
keywords:
- CentOS 7
- 系统初始化
- 性能优化
- 安全加固
- 生产环境
- 服务器配置
- 内核调优
- 容器化环境
- Kubernetes
- 企业级部署
description: "从基础配置到企业级优化的 CentOS 7 系统初始化完整指南，涵盖安全加固、性能调优、监控配置等生产环境最佳实践"
---

# CentOS 7 系统初始化简介

## 概述

CentOS 7 作为企业级 Linux 发行版，在生产环境中广泛应用。新安装的系统需要进行全面的初始化配置和优化，以确保系统的安全性、稳定性和高性能。本指南提供了一套经过生产环境验证的完整初始化方案。

### 适用场景

- **生产服务器部署**：Web 服务器、数据库服务器、应用服务器
- **容器化环境**：Docker 宿主机、Kubernetes 节点
- **云原生基础设施**：微服务架构、DevOps 平台
- **高可用集群**：负载均衡、分布式系统节点

### 优化目标

- **安全性**：系统安全加固、访问控制、审计配置
- **性能**：内核参数调优、网络优化、存储优化
- **稳定性**：服务配置、监控告警、故障恢复
- **可维护性**：标准化配置、自动化脚本、文档规范

## 系统要求

### 硬件要求

| 环境类型 | CPU | 内存 | 存储 | 网络 |
|----------|-----|------|------|------|
| **开发环境** | 2 核 | 4GB | 50GB | 100Mbps |
| **测试环境** | 4 核 | 8GB | 100GB | 1Gbps |
| **生产环境** | 8 核+ | 16GB+ | 500GB+ | 1Gbps+ |

### 软件版本

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **CentOS** | 7.6+ | 推荐 7.9 最新版本 |
| **内核** | 3.10+ | 推荐升级到 5.x LTS |
| **Docker** | 19.03+ | 容器化环境 |
| **Kubernetes** | 1.18+ | 容器编排平台 |

> **⚠️ 重要提示**: 本指南中的配置已在 CentOS 7.9 环境中验证，其他版本请根据实际情况调整

# 系统初始化配置

## 环境检查脚本

在开始配置之前，先运行环境检查脚本：

```bash
cat > system-check.sh << 'EOF'
#!/bin/bash

echo "=== CentOS 7 系统环境检查 ==="
echo "检查时间: $(date)"
echo

# 检查系统版本
echo "=== 系统信息 ==="
cat /etc/redhat-release
uname -a
echo

# 检查硬件资源
echo "=== 硬件资源 ==="
echo "CPU 信息:"
lscpu | grep -E "(Architecture|CPU|Thread|Core|Socket)"
echo
echo "内存信息:"
free -h
echo
echo "磁盘信息:"
df -h
echo

# 检查网络配置
echo "=== 网络配置 ==="
ip addr show
echo

# 检查服务状态
echo "=== 关键服务状态 ==="
systemctl status firewalld --no-pager -l
systemctl status NetworkManager --no-pager -l
echo

echo "=== 环境检查完成 ==="
EOF

chmod +x system-check.sh
./system-check.sh
```

## 系统性能优化

### 内核参数调优

创建企业级内核参数配置：

```bash
cat > /etc/sysctl.d/99-production.conf << 'EOF'
# ===== 网络性能优化 =====
# 启用桥接网络的 iptables 处理（Kubernetes 必需）
net.bridge.bridge-nf-call-ip6tables=1
net.bridge.bridge-nf-call-iptables=1

# 启用 IP 转发（容器网络必需）
net.ipv4.ip_forward=1
net.ipv4.conf.all.forwarding=1

# 反向路径过滤（安全优化）
net.ipv4.conf.all.rp_filter=0
net.ipv4.conf.default.rp_filter=0

# ARP 配置优化
net.ipv4.conf.default.arp_announce=2
net.ipv4.conf.lo.arp_announce=2
net.ipv4.conf.all.arp_announce=2

# 邻居表优化（提高网络性能）
net.ipv4.neigh.default.gc_thresh1=4096
net.ipv4.neigh.default.gc_thresh2=6144
net.ipv4.neigh.default.gc_thresh3=8192
net.ipv4.neigh.default.gc_interval=60
net.ipv4.neigh.default.gc_stale_time=120

# TCP 性能优化
net.ipv4.tcp_slow_start_after_idle=0
net.ipv4.tcp_max_syn_backlog=8096
net.ipv4.tcp_max_tw_buckets=5000
net.ipv4.tcp_syncookies=1
net.ipv4.tcp_fin_timeout=30
net.ipv4.tcp_synack_retries=2
net.ipv4.tcp_tw_reuse=1
net.ipv4.tcp_wmem=4096 12582912 16777216
net.ipv4.tcp_rmem=4096 12582912 16777216

# 网络缓冲区优化
net.core.rmem_max=16777216
net.core.wmem_max=16777216
net.core.netdev_max_backlog=16384
net.core.somaxconn=32768

# ===== 文件系统优化 =====
# 文件监控优化（适用于大量文件监控的应用）
fs.inotify.max_user_watches=524288
fs.inotify.max_user_instances=8192
fs.inotify.max_queued_events=16384

# 文件句柄限制
fs.file-max=2097152

# 内存映射区域限制（Elasticsearch 等应用需要）
vm.max_map_count=262144

# 允许分离挂载点（容器环境需要）
fs.may_detach_mounts=1

# ===== 安全配置 =====
# 硬链接和软链接保护
fs.protected_hardlinks=1
fs.protected_symlinks=1

# 禁用源路由（安全考虑）
net.ipv4.conf.default.accept_source_route=0
net.ipv4.conf.all.accept_source_route=0
net.ipv4.conf.default.accept_redirects=0
net.ipv4.conf.all.accept_redirects=0

# 地址提升配置
net.ipv4.conf.default.promote_secondaries=1
net.ipv4.conf.all.promote_secondaries=1

# ===== 内存管理优化 =====
# 禁用 swap（Kubernetes 推荐）
vm.swappiness=0

# 内存回收优化
vm.dirty_ratio=15
vm.dirty_background_ratio=5
vm.dirty_expire_centisecs=3000
vm.dirty_writeback_centisecs=500

# OOM 配置
vm.panic_on_oom=0
vm.oom_kill_allocating_task=1

# ===== 内核监控配置 =====
kernel.softlockup_all_cpu_backtrace=1
kernel.softlockup_panic=0
kernel.watchdog_thresh=30
kernel.sysrq=1

# 性能监控配置（Prometheus Node Exporter 需要）
kernel.perf_event_paranoid=-1

# 调试配置
kernel.yama.ptrace_scope=0
kernel.core_uses_pid=1
kernel.core_pattern=core.%e.%p.%t

# ===== IPv6 配置 =====
# 禁用 IPv6（如果不需要的话）
net.ipv6.conf.all.disable_ipv6=1
net.ipv6.conf.default.disable_ipv6=1
net.ipv6.conf.lo.disable_ipv6=1
EOF

# 应用配置
sysctl -p /etc/sysctl.d/99-production.conf

# 验证配置
echo "内核参数配置完成，验证关键参数："
sysctl net.ipv4.ip_forward
sysctl vm.swappiness
sysctl fs.file-max
```
### 系统文件句柄优化

```bash
# 创建文件句柄优化脚本
cat > optimize-limits.sh << 'EOF'
#!/bin/bash

echo "=== 优化系统文件句柄限制 ==="

# 备份原始配置
cp /etc/security/limits.conf /etc/security/limits.conf.bak

# 配置全局文件句柄限制
cat >> /etc/security/limits.conf << 'LIMITS'
# 全局文件句柄限制
* soft nofile 65536
* hard nofile 65536
* soft nproc 65536
* hard nproc 65536

# 特定用户限制（示例）
root soft nofile 65536
root hard nofile 65536
LIMITS

# 配置进程数限制
sed -i 's/4096/65536/g' /etc/security/limits.d/20-nproc.conf

# 验证配置
echo "当前配置："
grep -v "^#\|^$" /etc/security/limits.conf
echo
echo "进程限制配置："
cat /etc/security/limits.d/20-nproc.conf

echo "✓ 文件句柄优化完成"
EOF

chmod +x optimize-limits.sh
./optimize-limits.sh
```

## 内核升级

### 企业级内核升级脚本

```bash
cat > update-kernel.sh << 'EOF'
#!/bin/bash

##########################################################
# 企业级内核升级脚本
# 支持 LTS 长期支持版本和最新稳定版本
##########################################################

# 配置变量
KERNEL_TYPE="lt"  # lt: 长期支持版本, ml: 最新稳定版本
BACKUP_DIR="/root/kernel-backup"
LOG_FILE="/var/log/kernel-upgrade.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 环境检查
check_environment() {
    log "=== 环境检查 ==="

    # 检查系统版本
    if ! grep -q "CentOS Linux release 7" /etc/redhat-release; then
        log "错误: 此脚本仅支持 CentOS 7"
        exit 1
    fi

    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log "警告: 网络连接异常，可能影响下载"
    fi

    # 检查磁盘空间
    AVAILABLE_SPACE=$(df /boot | awk 'NR==2 {print $4}')
    if [ "$AVAILABLE_SPACE" -lt 500000 ]; then
        log "错误: /boot 分区空间不足 500MB"
        exit 1
    fi

    log "✓ 环境检查通过"
}

# 备份当前内核信息
backup_kernel_info() {
    log "=== 备份当前内核信息 ==="

    mkdir -p "$BACKUP_DIR"

    # 备份当前内核信息
    uname -a > "$BACKUP_DIR/current-kernel-$(date +%Y%m%d).txt"
    grub2-editenv list > "$BACKUP_DIR/grub-config-$(date +%Y%m%d).txt"

    log "当前内核版本: $(uname -r)"
    log "✓ 内核信息备份完成"
}

# 配置 ELRepo 仓库
setup_elrepo() {
    log "=== 配置 ELRepo 仓库 ==="

    # 导入 GPG 密钥
    rpm --import https://www.elrepo.org/RPM-GPG-KEY-elrepo.org

    # 安装 ELRepo 仓库
    rpm -Uvh http://www.elrepo.org/elrepo-release-7.0-3.el7.elrepo.noarch.rpm

    log "✓ ELRepo 仓库配置完成"
}

# 升级内核
upgrade_kernel() {
    log "=== 升级内核 ==="

    # 查看可用内核版本
    yum --disablerepo="*" --enablerepo="elrepo-kernel" list available | grep kernel-${KERNEL_TYPE}

    # 获取最新版本
    LATEST_VERSION=$(yum --disablerepo="*" --enablerepo="elrepo-kernel" list available | grep "kernel-${KERNEL_TYPE}\." | tail -1 | awk '{print $2}')

    if [ -z "$LATEST_VERSION" ]; then
        log "错误: 无法获取内核版本信息"
        exit 1
    fi

    log "准备安装内核版本: $LATEST_VERSION"

    # 安装新内核
    yum -y --enablerepo=elrepo-kernel install kernel-${KERNEL_TYPE} kernel-${KERNEL_TYPE}-devel

    if [ $? -eq 0 ]; then
        log "✓ 内核安装成功"
    else
        log "✗ 内核安装失败"
        exit 1
    fi
}

# 配置启动项
configure_grub() {
    log "=== 配置 GRUB 启动项 ==="

    # 获取新内核的启动项
    NEW_KERNEL=$(awk -F\' '$1=="menuentry " {print i++ " : " $2}' /etc/grub2.cfg | grep -E "kernel-${KERNEL_TYPE}" | head -1 | cut -d: -f2 | sed 's/^ *//')

    if [ -n "$NEW_KERNEL" ]; then
        grub2-set-default "$NEW_KERNEL"
        log "设置默认启动内核: $NEW_KERNEL"
    else
        log "警告: 无法自动设置启动项，请手动配置"
    fi

    # 重新生成 GRUB 配置
    grub2-mkconfig -o /boot/grub2/grub.cfg

    log "✓ GRUB 配置完成"
}

# 内核版本锁定
lock_kernel_version() {
    log "=== 锁定内核版本 ==="

    # 安装版本锁定工具
    yum -y install yum-versionlock

    # 锁定内核版本
    yum versionlock add kernel-${KERNEL_TYPE}*

    # 显示锁定的包
    yum versionlock list

    log "✓ 内核版本锁定完成"
}

# 清理旧内核
cleanup_old_kernels() {
    log "=== 清理旧内核 ==="

    # 保留最新的 2 个内核版本
    package-cleanup --oldkernels --count=2 -y

    log "✓ 旧内核清理完成"
}

# 主函数
main() {
    log "=== 开始内核升级 ==="

    check_environment
    backup_kernel_info
    setup_elrepo
    upgrade_kernel
    configure_grub
    lock_kernel_version

    log "=== 内核升级完成 ==="
    log "当前内核: $(uname -r)"
    log "新内核将在重启后生效"

    read -p "是否现在重启系统以使用新内核？[y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "系统重启中..."
        reboot
    else
        log "请稍后手动重启系统"
    fi
}

# 执行主函数
main "$@"
EOF

chmod +x update-kernel.sh

# 执行内核升级（可选）
echo "内核升级脚本已创建，如需升级请执行："
echo "./update-kernel.sh"
```

### 内核升级后验证

```bash
cat > verify-kernel.sh << 'EOF'
#!/bin/bash

echo "=== 内核升级验证 ==="

# 检查当前内核版本
echo "当前内核版本:"
uname -r

# 检查内核模块
echo -e "\n检查关键内核模块:"
lsmod | grep -E "(bridge|overlay|ip_tables|iptable_filter|iptable_nat)"

# 检查网络功能
echo -e "\n检查网络功能:"
if ip link show docker0 >/dev/null 2>&1; then
    echo "✓ Docker 网络正常"
else
    echo "ℹ Docker 未安装或未启动"
fi

# 检查容器功能（如果安装了 Docker）
if command -v docker >/dev/null 2>&1; then
    echo -e "\n检查容器功能:"
    docker run --rm hello-world >/dev/null 2>&1 && echo "✓ Docker 容器功能正常" || echo "⚠ Docker 容器功能异常"
fi

echo -e "\n=== 验证完成 ==="
EOF

chmod +x verify-kernel.sh
```

# 系统安全配置

## 网络接口配置

### 统一网卡名称为 ethx

```bash
cat > configure-network-interface.sh << 'EOF'
#!/bin/bash

echo "=== 配置网络接口名称 ==="

# 备份 GRUB 配置
cp /etc/default/grub /etc/default/grub.bak

# 修改 GRUB 配置，统一网卡名称
sed -i 's/GRUB_CMDLINE_LINUX="\(.*\)"/GRUB_CMDLINE_LINUX="net.ifnames=0 biosdevname=0 cgroup_enable=memory swapaccount=1 \1"/g' /etc/default/grub

# 重新生成 GRUB 配置
grub2-mkconfig -o /boot/grub2/grub.cfg

echo "✓ 网络接口配置完成"
echo "重启后网卡将使用 eth0, eth1... 命名方式"
EOF

chmod +x configure-network-interface.sh
./configure-network-interface.sh
```

## SELinux 配置

### 安全策略配置

```bash
cat > configure-selinux.sh << 'EOF'
#!/bin/bash

echo "=== 配置 SELinux 安全策略 ==="

# 备份原始配置
cp /etc/selinux/config /etc/selinux/config.bak

# 检查当前状态
echo "当前 SELinux 状态:"
getenforce
sestatus

# 根据环境选择配置
read -p "选择 SELinux 模式 [1=Disabled, 2=Permissive, 3=Enforcing]: " choice

case $choice in
    1)
        echo "配置为 Disabled 模式（生产环境推荐）"
        sed -i 's/^SELINUX=.*/SELINUX=disabled/' /etc/selinux/config
        setenforce 0 2>/dev/null || true
        ;;
    2)
        echo "配置为 Permissive 模式（调试环境）"
        sed -i 's/^SELINUX=.*/SELINUX=permissive/' /etc/selinux/config
        setenforce 0
        ;;
    3)
        echo "保持 Enforcing 模式（高安全环境）"
        sed -i 's/^SELINUX=.*/SELINUX=enforcing/' /etc/selinux/config
        # 配置容器相关策略
        setsebool -P container_manage_cgroup on
        ;;
    *)
        echo "无效选择，保持默认配置"
        ;;
esac

echo "当前配置:"
grep "^SELINUX=" /etc/selinux/config
echo "✓ SELinux 配置完成"
EOF

chmod +x configure-selinux.sh
./configure-selinux.sh
```

## 防火墙配置

### 企业级防火墙配置

```bash
cat > configure-firewall.sh << 'EOF'
#!/bin/bash

echo "=== 配置企业级防火墙 ==="

# 检查防火墙状态
systemctl status firewalld --no-pager

read -p "选择防火墙配置 [1=关闭, 2=基础配置, 3=严格配置]: " fw_choice

case $fw_choice in
    1)
        echo "关闭防火墙（内网环境）"
        systemctl stop firewalld
        systemctl disable firewalld
        echo "✓ 防火墙已关闭"
        ;;
    2)
        echo "配置基础防火墙规则"
        systemctl enable firewalld
        systemctl start firewalld

        # 基础服务端口
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https

        # 常用端口
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --permanent --add-port=9090/tcp

        firewall-cmd --reload
        echo "✓ 基础防火墙配置完成"
        ;;
    3)
        echo "配置严格防火墙规则"
        systemctl enable firewalld
        systemctl start firewalld

        # 设置默认区域
        firewall-cmd --set-default-zone=public

        # 仅允许必要服务
        firewall-cmd --permanent --add-service=ssh

        # 限制 SSH 访问（示例：仅允许特定网段）
        firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="***********/16" service name="ssh" accept'
        firewall-cmd --permanent --remove-service=ssh

        firewall-cmd --reload
        echo "✓ 严格防火墙配置完成"
        ;;
    *)
        echo "保持默认防火墙配置"
        ;;
esac

# 显示当前规则
echo "当前防火墙规则:"
firewall-cmd --list-all 2>/dev/null || echo "防火墙未启用"
EOF

chmod +x configure-firewall.sh
./configure-firewall.sh
```

## SSH 安全加固

### SSH 服务优化配置

```bash
cat > harden-ssh.sh << 'EOF'
#!/bin/bash

echo "=== SSH 安全加固 ==="

# 备份原始配置
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak

# 创建安全配置
cat > /etc/ssh/sshd_config.secure << 'SSHCONFIG'
# SSH 安全配置
Port 22
Protocol 2

# 认证配置
PermitRootLogin no
PasswordAuthentication yes
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys

# 安全选项
UseDNS no
GSSAPIAuthentication no
GSSAPICleanupCredentials no
X11Forwarding no
AllowTcpForwarding no
PermitTunnel no

# 连接限制
MaxAuthTries 3
MaxSessions 10
MaxStartups 10:30:100
LoginGraceTime 60

# 会话配置
ClientAliveInterval 300
ClientAliveCountMax 2
TCPKeepAlive yes

# 日志配置
SyslogFacility AUTHPRIV
LogLevel INFO

# 允许的用户和组（根据需要修改）
# AllowUsers user1 user2
# AllowGroups wheel

# 禁用空密码
PermitEmptyPasswords no

# 严格模式
StrictModes yes

# 主机密钥
HostKey /etc/ssh/ssh_host_rsa_key
HostKey /etc/ssh/ssh_host_ecdsa_key
HostKey /etc/ssh/ssh_host_ed25519_key
SSHCONFIG

# 应用配置
read -p "是否应用 SSH 安全配置？[y/N]: " apply_ssh
if [[ $apply_ssh =~ ^[Yy]$ ]]; then
    cp /etc/ssh/sshd_config.secure /etc/ssh/sshd_config

    # 测试配置
    sshd -t
    if [ $? -eq 0 ]; then
        systemctl restart sshd
        echo "✓ SSH 安全配置已应用"
    else
        echo "✗ SSH 配置有误，已恢复原始配置"
        cp /etc/ssh/sshd_config.bak /etc/ssh/sshd_config
    fi
else
    echo "SSH 配置已保存到 /etc/ssh/sshd_config.secure"
fi

echo "✓ SSH 安全加固完成"
EOF

chmod +x harden-ssh.sh
./harden-ssh.sh
```

# 软件包管理与优化

## YUM 源配置

### 配置国内镜像源

```bash
cat > configure-yum-repos.sh << 'EOF'
#!/bin/bash

echo "=== 配置 YUM 镜像源 ==="

# 备份原始仓库配置
if [ -d /etc/yum.repos.d ]; then
    mv /etc/yum.repos.d /etc/yum.repos.d.backup.$(date +%Y%m%d)
fi

mkdir -p /etc/yum.repos.d

# 配置阿里云镜像源
echo "配置阿里云镜像源..."
curl -o /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-7.repo
wget -O /etc/yum.repos.d/epel.repo http://mirrors.aliyun.com/repo/epel-7.repo

# 配置 Docker CE 仓库
echo "配置 Docker CE 仓库..."
yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo

# 配置 Kubernetes 仓库
echo "配置 Kubernetes 仓库..."
cat > /etc/yum.repos.d/kubernetes.repo << 'K8SREPO'
[kubernetes]
name=Kubernetes
baseurl=https://mirrors.aliyun.com/kubernetes/yum/repos/kubernetes-el7-x86_64/
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://mirrors.aliyun.com/kubernetes/yum/doc/yum-key.gpg https://mirrors.aliyun.com/kubernetes/yum/doc/rpm-package-key.gpg
K8SREPO

# 清理并重建缓存
yum clean all
yum makecache fast

echo "✓ YUM 源配置完成"
yum repolist
EOF

chmod +x configure-yum-repos.sh
./configure-yum-repos.sh
```

## 基础软件安装

### 系统工具安装

```bash
cat > install-base-tools.sh << 'EOF'
#!/bin/bash

echo "=== 安装基础系统工具 ==="

# 基础工具包
BASE_TOOLS=(
    "vim"
    "wget"
    "curl"
    "git"
    "tree"
    "htop"
    "iotop"
    "iftop"
    "lsof"
    "strace"
    "tcpdump"
    "telnet"
    "nc"
    "rsync"
    "screen"
    "tmux"
    "bash-completion"
    "yum-utils"
    "device-mapper-persistent-data"
    "lvm2"
)

# 开发工具包
DEV_TOOLS=(
    "gcc"
    "gcc-c++"
    "make"
    "cmake"
    "autoconf"
    "automake"
    "libtool"
    "pkgconfig"
)

# 网络工具包
NET_TOOLS=(
    "net-tools"
    "bind-utils"
    "traceroute"
    "mtr"
    "nmap"
    "wireshark"
)

# 系统监控工具
MONITOR_TOOLS=(
    "sysstat"
    "dstat"
    "atop"
    "nethogs"
    "iperf3"
)

# 安装函数
install_packages() {
    local packages=("$@")
    echo "安装软件包: ${packages[*]}"

    yum install -y "${packages[@]}"

    if [ $? -eq 0 ]; then
        echo "✓ 软件包安装成功"
    else
        echo "✗ 软件包安装失败"
        return 1
    fi
}

# 主安装流程
main() {
    echo "开始安装基础工具..."

    # 更新系统
    yum update -y

    # 安装各类工具
    install_packages "${BASE_TOOLS[@]}"
    install_packages "${DEV_TOOLS[@]}"
    install_packages "${NET_TOOLS[@]}"
    install_packages "${MONITOR_TOOLS[@]}"

    # 安装 EPEL 仓库
    yum install -y epel-release

    echo "✓ 基础工具安装完成"
}

main "$@"
EOF

chmod +x install-base-tools.sh
./install-base-tools.sh
```

## 时间同步配置

### NTP/Chrony 时间同步

```bash
cat > configure-time-sync.sh << 'EOF'
#!/bin/bash

echo "=== 配置时间同步服务 ==="

# 选择时间同步服务
read -p "选择时间同步服务 [1=Chrony(推荐), 2=NTP]: " time_service

case $time_service in
    1)
        echo "配置 Chrony 时间同步..."

        # 安装 Chrony
        yum install -y chrony

        # 配置 Chrony
        cat > /etc/chrony.conf << 'CHRONYCONF'
# 使用阿里云 NTP 服务器
server ntp.aliyun.com iburst
server ntp1.aliyun.com iburst
server ntp2.aliyun.com iburst
server ntp3.aliyun.com iburst

# 备用 NTP 服务器
server 0.centos.pool.ntp.org iburst
server 1.centos.pool.ntp.org iburst

# 记录系统时钟获得/丢失时间的速率
driftfile /var/lib/chrony/drift

# 允许系统时钟在前三次更新中步进
makestep 1.0 3

# 启用内核同步 RTC
rtcsync

# 日志目录
logdir /var/log/chrony
CHRONYCONF

        # 启动服务
        systemctl enable chronyd
        systemctl start chronyd

        # 验证同步状态
        chrony sources -v
        ;;
    2)
        echo "配置 NTP 时间同步..."

        # 安装 NTP
        yum install -y ntp ntpdate

        # 配置 NTP
        cat > /etc/ntp.conf << 'NTPCONF'
# 使用阿里云 NTP 服务器
server ntp.aliyun.com prefer
server ntp1.aliyun.com
server ntp2.aliyun.com
server ntp3.aliyun.com

# 备用 NTP 服务器
server 0.centos.pool.ntp.org
server 1.centos.pool.ntp.org

# 限制访问
restrict default nomodify notrap nopeer noquery
restrict 127.0.0.1
restrict ::1

# 日志文件
logfile /var/log/ntp.log

# 漂移文件
driftfile /var/lib/ntp/drift
NTPCONF

        # 启动服务
        systemctl enable ntpd
        systemctl start ntpd

        # 验证同步状态
        ntpq -p
        ;;
    *)
        echo "保持默认时间配置"
        ;;
esac

# 设置时区
timedatectl set-timezone Asia/Shanghai

# 显示时间状态
timedatectl status

echo "✓ 时间同步配置完成"
EOF

chmod +x configure-time-sync.sh
./configure-time-sync.sh
```

## 用户安全配置

### 终端安全设置

```bash
cat > configure-user-security.sh << 'EOF'
#!/bin/bash

echo "=== 配置用户安全设置 ==="

# 配置终端超时
echo "配置终端自动超时..."
cat >> /etc/profile << 'PROFILE'

# 终端超时设置（30分钟）
export TMOUT=1800
readonly TMOUT

# 历史命令配置
export HISTSIZE=1000
export HISTFILESIZE=2000
export HISTTIMEFORMAT="%Y-%m-%d %H:%M:%S "
export HISTCONTROL=ignoredups:erasedups

# 安全提示
echo "欢迎使用 $(hostname) 系统"
echo "当前时间: $(date)"
echo "系统负载: $(uptime | awk -F'load average:' '{print $2}')"
PROFILE

# 配置密码策略
echo "配置密码策略..."
cat >> /etc/login.defs << 'LOGINDEFS'

# 密码策略配置
PASS_MAX_DAYS   90
PASS_MIN_DAYS   1
PASS_MIN_LEN    8
PASS_WARN_AGE   7
LOGINDEFS

# 配置 PAM 密码复杂度
if [ -f /etc/pam.d/system-auth ]; then
    # 备份原始文件
    cp /etc/pam.d/system-auth /etc/pam.d/system-auth.bak

    # 添加密码复杂度要求
    sed -i '/pam_pwquality.so/c\password    requisite     pam_pwquality.so try_first_pass local_users_only retry=3 minlen=8 dcredit=-1 ucredit=-1 ocredit=-1 lcredit=-1' /etc/pam.d/system-auth
fi

# 配置登录失败锁定
cat >> /etc/pam.d/sshd << 'PAMLOCKOUT'
# 登录失败锁定配置
auth required pam_tally2.so deny=5 unlock_time=300 even_deny_root root_unlock_time=300
PAMLOCKOUT

echo "✓ 用户安全配置完成"
source /etc/profile
EOF

chmod +x configure-user-security.sh
./configure-user-security.sh
```

# 容器化环境配置

## Docker 安装与优化

### 企业级 Docker 安装脚本

```bash
cat > install-docker.sh << 'EOF'
#!/bin/bash

echo "=== 企业级 Docker 安装脚本 ==="

DOCKER_VERSION="20.10.21"
DOCKER_COMPOSE_VERSION="2.12.2"

# 环境检查
check_environment() {
    echo "检查系统环境..."

    # 检查内核版本
    KERNEL_VERSION=$(uname -r | cut -d. -f1-2)
    if [ "$(echo "$KERNEL_VERSION >= 3.10" | bc)" -eq 0 ]; then
        echo "错误: 内核版本过低，需要 3.10 或更高版本"
        exit 1
    fi

    # 检查存储驱动支持
    if ! grep -q overlay /proc/filesystems; then
        echo "警告: 系统不支持 overlay2 存储驱动"
    fi

    echo "✓ 环境检查通过"
}

# 安装 Docker
install_docker() {
    echo "安装 Docker CE..."

    # 卸载旧版本
    yum remove -y docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine

    # 安装依赖
    yum install -y yum-utils device-mapper-persistent-data lvm2

    # 添加 Docker 仓库
    yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo

    # 更新缓存
    yum makecache fast

    # 安装指定版本的 Docker
    if [ -n "$DOCKER_VERSION" ]; then
        yum install -y docker-ce-${DOCKER_VERSION} docker-ce-cli-${DOCKER_VERSION} containerd.io
    else
        yum install -y docker-ce docker-ce-cli containerd.io
    fi

    echo "✓ Docker 安装完成"
}

# 配置 Docker
configure_docker() {
    echo "配置 Docker..."

    # 创建配置目录
    mkdir -p /etc/docker

    # 创建 Docker 配置文件
    cat > /etc/docker/daemon.json << 'DOCKERCONFIG'
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "storage-opts": [
        "overlay2.override_kernel_check=true"
    ],
    "exec-opts": ["native.cgroupdriver=systemd"],
    "registry-mirrors": [
        "https://mirror.ccs.tencentyun.com",
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com"
    ],
    "insecure-registries": [
        "harbor.company.com",
        "registry.company.com"
    ],
    "max-concurrent-downloads": 10,
    "max-concurrent-uploads": 5,
    "default-address-pools": [
        {
            "base": "**********/16",
            "size": 24
        }
    ],
    "oom-score-adjust": -1000,
    "live-restore": true,
    "userland-proxy": false,
    "experimental": false,
    "metrics-addr": "0.0.0.0:9323",
    "iptables": true,
    "ip-forward": true,
    "ip-masq": true,
    "ipv6": false,
    "fixed-cidr-v6": "",
    "default-gateway": "",
    "default-gateway-v6": "",
    "bridge": "",
    "bip": "",
    "mtu": 0,
    "default-ulimits": {
        "nofile": {
            "Name": "nofile",
            "Hard": 64000,
            "Soft": 64000
        }
    }
}
DOCKERCONFIG

    echo "✓ Docker 配置完成"
}

# 优化 Docker 服务
optimize_docker_service() {
    echo "优化 Docker 服务..."

    # 创建 systemd 覆盖目录
    mkdir -p /etc/systemd/system/docker.service.d

    # 创建服务优化配置
    cat > /etc/systemd/system/docker.service.d/override.conf << 'SERVICECONFIG'
[Service]
# 防止 OOM 杀死 Docker 守护进程
OOMScoreAdjust=-1000

# 设置文件描述符限制
LimitNOFILE=1048576
LimitNPROC=1048576
LimitCORE=infinity

# 设置启动后执行的命令
ExecStartPost=/usr/sbin/iptables -P FORWARD ACCEPT

# 重启策略
Restart=always
RestartSec=5
SERVICECONFIG

    echo "✓ Docker 服务优化完成"
}

# 安装 Docker Compose
install_docker_compose() {
    echo "安装 Docker Compose..."

    # 下载 Docker Compose
    curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

    # 设置执行权限
    chmod +x /usr/local/bin/docker-compose

    # 创建软链接
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose

    # 验证安装
    docker-compose --version

    echo "✓ Docker Compose 安装完成"
}

# 启动和验证
start_and_verify() {
    echo "启动 Docker 服务..."

    # 重新加载 systemd 配置
    systemctl daemon-reload

    # 启动 Docker
    systemctl start docker
    systemctl enable docker

    # 验证安装
    echo "验证 Docker 安装..."
    docker --version
    docker info

    # 运行测试容器
    echo "运行测试容器..."
    docker run --rm hello-world

    if [ $? -eq 0 ]; then
        echo "✓ Docker 安装验证成功"
    else
        echo "✗ Docker 安装验证失败"
        exit 1
    fi
}

# 主函数
main() {
    check_environment
    install_docker
    configure_docker
    optimize_docker_service
    install_docker_compose
    start_and_verify

    echo "=== Docker 安装完成 ==="
    echo "Docker 版本: $(docker --version)"
    echo "Docker Compose 版本: $(docker-compose --version)"
    echo "配置文件: /etc/docker/daemon.json"
    echo "服务配置: /etc/systemd/system/docker.service.d/override.conf"
}

main "$@"
EOF

chmod +x install-docker.sh
./install-docker.sh
```

## 系统监控配置

### 基础监控脚本

```bash
cat > system-monitor.sh << 'EOF'
#!/bin/bash

echo "=== 系统监控信息 ==="
echo "检查时间: $(date)"
echo

# 系统负载
echo "=== 系统负载 ==="
uptime
echo

# CPU 使用率
echo "=== CPU 使用率 ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2 $4}'
echo

# 内存使用情况
echo "=== 内存使用情况 ==="
free -h
echo

# 磁盘使用情况
echo "=== 磁盘使用情况 ==="
df -h | grep -vE '^Filesystem|tmpfs|cdrom'
echo

# 网络连接
echo "=== 网络连接统计 ==="
ss -tuln | wc -l
echo "总连接数: $(ss -tuln | wc -l)"
echo

# 进程统计
echo "=== 进程统计 ==="
echo "总进程数: $(ps aux | wc -l)"
echo "运行中进程: $(ps aux | awk '$8 ~ /^R/ {count++} END {print count+0}')"
echo

# Docker 状态（如果安装了）
if command -v docker >/dev/null 2>&1; then
    echo "=== Docker 状态 ==="
    echo "Docker 版本: $(docker --version)"
    echo "运行中容器: $(docker ps -q | wc -l)"
    echo "总容器数: $(docker ps -aq | wc -l)"
    echo "镜像数量: $(docker images -q | wc -l)"
    echo
fi

echo "=== 监控信息收集完成 ==="
EOF

chmod +x system-monitor.sh
```

# 总结

## 配置验证脚本

```bash
cat > verify-configuration.sh << 'EOF'
#!/bin/bash

echo "=== CentOS 7 配置验证脚本 ==="
echo "验证时间: $(date)"
echo

PASS=0
FAIL=0

# 验证函数
verify() {
    local test_name="$1"
    local command="$2"

    echo -n "检查 $test_name: "

    if eval "$command" >/dev/null 2>&1; then
        echo "✓ 通过"
        ((PASS++))
    else
        echo "✗ 失败"
        ((FAIL++))
    fi
}

# 系统配置验证
echo "=== 系统配置验证 ==="
verify "内核参数" "sysctl net.ipv4.ip_forward | grep -q 1"
verify "文件句柄限制" "ulimit -n | grep -q 65536"
verify "SELinux 状态" "getenforce | grep -qE 'Disabled|Permissive'"
verify "时间同步" "systemctl is-active chronyd || systemctl is-active ntpd"

# 网络配置验证
echo -e "\n=== 网络配置验证 ==="
verify "网络连通性" "ping -c 1 *******"
verify "DNS 解析" "nslookup google.com"

# 服务状态验证
echo -e "\n=== 服务状态验证 ==="
verify "SSH 服务" "systemctl is-active sshd"

# Docker 验证（如果安装了）
if command -v docker >/dev/null 2>&1; then
    echo -e "\n=== Docker 验证 ==="
    verify "Docker 服务" "systemctl is-active docker"
    verify "Docker 功能" "docker run --rm hello-world"
fi

# 安全配置验证
echo -e "\n=== 安全配置验证 ==="
verify "防火墙状态" "systemctl is-active firewalld || systemctl is-failed firewalld"
verify "SSH 配置" "grep -q 'UseDNS no' /etc/ssh/sshd_config"

# 总结
echo -e "\n=== 验证结果 ==="
echo "通过: $PASS 项"
echo "失败: $FAIL 项"

if [ $FAIL -eq 0 ]; then
    echo "✓ 所有配置验证通过"
    exit 0
else
    echo "⚠ 有 $FAIL 项配置需要检查"
    exit 1
fi
EOF

chmod +x verify-configuration.sh
./verify-configuration.sh
```

## 部署优势

通过本指南的配置，您的 CentOS 7 系统将具备以下优势：

### 性能优势
- **内核优化**：针对容器化和高并发场景的内核参数调优
- **网络优化**：TCP/IP 栈优化，提升网络性能
- **存储优化**：文件系统和 I/O 性能优化
- **资源管理**：合理的资源限制和调度策略

### 安全优势
- **系统加固**：SELinux、防火墙、SSH 安全配置
- **访问控制**：用户权限管理和登录安全
- **审计日志**：完整的系统操作审计
- **漏洞防护**：及时的安全更新和补丁管理

### 运维优势
- **标准化配置**：统一的系统配置标准
- **自动化脚本**：完整的自动化部署脚本
- **监控集成**：系统监控和告警机制
- **故障排除**：完善的诊断和恢复工具

## 最佳实践

### 生产环境建议
1. **定期更新**：保持系统和软件包的及时更新
2. **备份策略**：建立完善的系统和数据备份机制
3. **监控告警**：部署全面的系统监控和告警
4. **文档维护**：保持配置文档的及时更新
5. **安全审计**：定期进行安全检查和漏洞扫描

### 扩展建议
- **集群部署**：配置高可用和负载均衡
- **容器编排**：集成 Kubernetes 或 Docker Swarm
- **CI/CD 集成**：与 Jenkins、GitLab CI 等工具集成
- **监控平台**：部署 Prometheus、Grafana 等监控方案

通过本指南的配置和最佳实践，您可以构建一个安全、稳定、高性能的企业级 CentOS 7 系统，为各种应用场景提供可靠的基础平台支撑。
