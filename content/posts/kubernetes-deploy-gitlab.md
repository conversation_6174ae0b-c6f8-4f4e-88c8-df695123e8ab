---
title: "在 Kubernetes 中使用 localPv 部署 Gitlab"
date: 2021-06-01T10:13:29+08:00
draft: false
tags: [ "gitlab","ci-cd","localPV","postgres","redis"]
tags_weight: 20
categories: ["k8s","devops","ingress"]
categories_weight: 20
keywords:
- maven
- ci-cd
- jenkins
- devops
- kubernetes
- localPV
- redis
- postgres
- 部署
- docker
description: "在 Kubernetes 中使用 localPv 部署 Gitlab,并配置关联使用外部的 postgres & redis 进行使用，降低系统资源"
---



# 环境说明

- Kubernetes Version: `v1.20.4`
- Postgres Version:  `12.7`
- Redis Version: `5.0.9`

> 因在官方提供的 `gitlab-ce` 镜像中，内置了 Postgres & Redis 的安装，在实际生产使用过程中，想让其连接使用外部统一的服务进行使用，来合理统一的管理，并有效降低对应资源使用率，这里使用 dokcer 非官方镜像: `sameersbn/gitlab:13.12.1` 进行使用



# Postgres & Redis 的安装

> postgres & redis 的安装，使用了 [`oneinstack`](https://oneinstack.com/) 一键工具，进行编译安装的管理

```bash
wget http://mirrors.linuxeye.com/oneinstack-full.tar.gz

tar xzf oneinstack-full.tar.gz

cd oneinstack 

./install.sh  # 交互式选择安装 redis &  postgres 数据即可
```

> 由于 gitlab 中使用了 postgres 中的 扩展组件，这里还需要进行编译安装一下，步骤如下: 
>
> ```bash
> cd /data/scripts/oneinstack/src/postgresql-12.7/contrib/ && make -j8 && make install # 对应目录，按实际境况更改一下
> ```

未编译安装，创建扩展时报错提示：

![image-20210604083024083](https://cdn.treesir.pub/img/image-20210604083024083.png)



##  postgres 数据库初始化准备

```bash
su - postgres  
psql  
CREATE USER gitlab WITH PASSWORD '123456';
CREATE DATABASE gitlab_production OWNER gitlab;  # 创建 registry 数据库
ALTER USER gitlab with createdb;


\c gitlab_production  # 进入刚才创建的数据库
CREATE EXTENSION pg_trgm; 
CREATE EXTENSION btree_gist;

GRANT ALL PRIVILEGES ON DATABASE gitlab_production to gitlab;
GRANT ALL PRIVILEGES ON all tables in schema public TO gitlab;

\l     ; 
postgres=# \q  # 退出；
```

> 如在安装过程中，想清理重置一下 数据库配置，可执行以下命令:
>
> ```bash
> su - postgres  
> psql  
> 
> drop database gitlab_production;
> drop owned by gitlab;
> drop role gitlab;
> ```



## redis 监听地址优化

> 默认 redis 安装后，且监听在 `127.0.0.1` 之上，即且本地可使用，这里需要进行更改一下监听地址。

```bash
sed -i "s#bind 127.0.0.1#bind 0.0.0.0#g" # 更改为监听所有，非推荐做法。
```


# Gitlab Kubernetes 中的安装

> 准备好 postgres & redis 并做好了，对应的初始化步骤后，就可以进行在 k8s 中进行安装部署了。下面示例的 部署文件中，包涵了 关联集成 `smtp` & `openLdap`，并创建使用 traefik 进行对应页面的暴露使用。

## 创建部署 namespace

```bash
kubectl create ns gitlab
```

## 部署 `gitlab.yaml` 部署文件如下所示

```yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: gitlab-pv
spec:
  storageClassName: local  # Local PV
  capacity:
    storage: 100Gi
  volumeMode: Filesystem
  accessModes:
  - ReadWriteOnce
  local:
    path: /data/gitlab/data/
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - node2
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitlab-pvc
  namespace: gitlab
spec:
  storageClassName: local
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab
  namespace: gitlab
  labels:
    name: gitlab
spec:
  selector:
    matchLabels:
      name: gitlab
  template:
    metadata:
      name: gitlab
      labels:
        name: gitlab
    spec:
      initContainers:
      - name: fix-permissions
        image: busybox
        command: ["sh", "-c", "chown -R 1000:1000 /home/<USER>/data"]
        securityContext:
          privileged: true
        volumeMounts:
        - name: data
          mountPath: /home/<USER>/data
      containers:
      - name: gitlab
        image: sameersbn/gitlab:13.12.1
        imagePullPolicy: IfNotPresent
        env:
        - name: TZ
          value: Asia/Shanghai
        - name: GITLAB_TIMEZONE
          value: Beijing
        - name: GITLAB_SECRETS_DB_KEY_BASE
          value: long-and-random-alpha-numeric-string
        - name: GITLAB_SECRETS_SECRET_KEY_BASE
          value: long-and-random-alpha-numeric-string
        - name: GITLAB_SECRETS_OTP_KEY_BASE
          value: long-and-random-alpha-numeric-string
        - name: GITLAB_ROOT_PASSWORD
          value: treesir123
        - name: GITLAB_ROOT_EMAIL
          value: <EMAIL>
        - name: GITLAB_HOST
          value: gitlab.treesir.pub
        - name: GITLAB_PORT
          value: "80"
        - name: GITLAB_SSH_PORT
          value: "22"
        - name: GITLAB_NOTIFY_ON_BROKEN_BUILDS
          value: "true"
        - name: GITLAB_NOTIFY_PUSHER
          value: "false"
        - name: GITLAB_BACKUP_SCHEDULE
          value: daily
        - name: GITLAB_BACKUP_TIME
          value: 01:00
        - name: DB_TYPE
          value: postgres
        - name: DB_HOST
          value: ************
        - name: DB_PORT
          value: '5432'
        - name: DB_USER
          value: gitlab
        - name: DB_PASS
          value: "123456"
        - name: DB_NAME
          value: gitlab_production
        - name: REDIS_HOST
          value: ************
        - name: REDIS_PORT
          value: "6379"
        - name: SMTP_ENABLED  # 配置开启 smtp
          value: 'true'
        - name: SMTP_DOMAIN
          value: mail.163.com
        - name: SMTP_HOST
          value: smtp.163.com
        - name: SMTP_PORT
          value: '465'
        - name: SMTP_USER
          value: <EMAIL>
        - name: SMTP_PASS
          value: xxx
        - name: SMTP_TLS
          value: 'true'
        - name: LDAP_ENABLED
          value: 'true'
        - name: LDAP_HOST
          value: ***********
        - name: LDAP_UID
          value: uid
        - name: LDAP_BIND_DN
          value: cn=admin,dc=treesir,dc=pub
        - name: LDAP_PASS
          value: '123456'
        - name: LDAP_ACTIVE_DIRECTORY
          value: 'false'
        - name: LDAP_ALLOW_USERNAME_OR_EMAIL_LOGIN
          value: 'false'
        - name: LDAP_BASE
          value: ou=users,dc=treesir,dc=pub
        ports:
        - name: http
          containerPort: 80
        - name: ssh
          containerPort: 22
        volumeMounts:
        - mountPath: /home/<USER>/data
          name: data
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 60
          timeoutSeconds: 1
        resources:
          limits:
            cpu: 4000m
            memory: 6144Mi
          requests:
            cpu: 1000m
            memory: 2048Mi
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: gitlab-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: gitlab
  namespace: gitlab
  labels:
    name: gitlab
spec:
  ports:
    - name: http
      port: 80
      targetPort: http
    - name: ssh
      port: 22
      targetPort: ssh
  selector:
    name: gitlab
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: gitlab
  namespace: gitlab
spec:
  entryPoints:
  - web
  routes:
  - kind: Rule
    match: Host(`gitlab.treesir.pub`)
    services:
    - name: gitlab
      port: 80
```

> 更加高级扩展功能，请查看此 `容器` 的环境变量 [配置表](https://github.com/sameersbn/docker-gitlab#available-configuration-parameters)，注意使用 `localPv` 进行关联部署的话，请注意一下，在对应的节点中是否有对应的 `目录存在`。

![image-20210601101927204](https://cdn.treesir.pub/img/image-20210601101927204.png)

## 等待初始化完成

```bash
watch kubectl get po -n gitlab
```

![image-20210601102152909](https://cdn.treesir.pub/img/image-20210601102152909.png)

![image-20210601111600155](https://cdn.treesir.pub/img/image-20210601111600155.png)

![image-20210601102405269](https://cdn.treesir.pub/img/image-20210601102405269.png)

## 测试使用 ldap 进行使用

![image-20210601111648512](https://cdn.treesir.pub/img/image-20210601111648512.png)

# 参考文档

https://www.treesir.pub/post/ingress-traefik/

https://www.treesir.pub/post/docker-deploy-ldap/

https://www.treesir.pub/post/gitlab-deploy/

https://github.com/sameersbn/docker-gitlab
