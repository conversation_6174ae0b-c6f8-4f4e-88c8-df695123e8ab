---

title: "记录一次 Nexus3 Pypi 私服 Blob Storage 异常的修复"
date: 2021-07-22T15:38:00+08:00
draft: false
tags: ["neuxs3","fix","pypi","jenkins","pipeline"]
tags_weight: 20
categories: ["devops"]
categories_weight: 20
keywords:
- neuxs3
- devops
- fix
- pypi
- 私服
- 下载失败
- error
- blob
- orientdb
- pipeline
- jenkins
description: "devops jenkins pipeline 使用 Nexus3 Pypi 私服 时 Blob Storage 异常无法使用的修复过程记录。"
---



# 说明

> 在 devops 集成环境中的 `测试环境` 中，今天发现有几条 pipeline 一直是在构建超时状态。初步查看了一下，以为是哪个 依赖版本的兼容性有些问题, 导致 pip 一直是找不到合适的版本，和开发一起进行了一次排查，在 开发环境上即正常，测试环境就不行了，而且使用的是同一套代码 和 Dockerfile 文件，排除与版本有关，具体错误表现如下图所示：

![image-20210721170700491](https://cdn.treesir.pub/img/image-20210721170700491.png)

**滚动的检查 nexus3 私服日志**

```bash
docker logs  -f --tail 100 nexus3
```

![image-20210721171037634](https://cdn.treesir.pub/img/image-20210721171037634.png)

![image-20210721171052792](https://cdn.treesir.pub/img/image-20210721171052792.png)

> 去对应的 blob 看对应的文件内容也是空的，初步结合日志得出，是 blobstorage 的对应的 存储数据 丢失导致。

# 修复过程

## 尝试修复方法 (一)

> 尝试对数据库进行重建索引看看，看是否能修复成功。( 内部 nexus3 使用的是一个叫做  `orientdb` 的数据库，相关的 [文档](https://orientdb.com/docs/last/dotnet/NET-Transactions-Delete.html) 查看。)

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# U/P: admin admin  默认密码
CONNECT PLOCAL:/nexus-data/db/component/ admin admin  
REBUILD INDEX *
REPAIR DATABASE --fix-graph
REPAIR DATABASE --fix-links
REPAIR DATABASE --fix-ridbags
REPAIR DATABASE --fix-bonsai
DISCONNECT
```

![image-20210721172248837](https://cdn.treesir.pub/img/image-20210721172248837.png)

再次触发一条 pipeline 测试效果，还是一样的，没有什么效果。

![image-20210721172720110](https://cdn.treesir.pub/img/image-20210721172720110.png)

## 尝试修复方法 (二)

> 此方法的方式为:  将 nexus3 的数据进行备份，将老的数据删除，然后基于备份好的数据进行恢复。

**创建 备份数据 task 任务**

![image-20210721172844252](https://cdn.treesir.pub/img/image-20210721172844252.png)



**选择 备份导出数据库**

![image-20210721172857718](https://cdn.treesir.pub/img/image-20210721172857718.png)



**设置备份任务**

![image-20210721173128605](https://cdn.treesir.pub/img/image-20210721173128605.png)

**启动备份任务**

![image-20210721173158292](https://cdn.treesir.pub/img/image-20210721173158292.png)

**等待任务执行 成功**

![image-20210722160039770](https://cdn.treesir.pub/img/image-20210722160039770.png)



> 由于在导出过程中，被 `中断` 数据无法导出，此方法 `暂且放弃`。



## 尝试修复方法 (三)

> 此方法为: 尝试创建 `task` 任务 进行一个 blob storage 存储的 `元数据修复` 工作



**选择创建 修复 blob 元数据 task**

![image-20210722160512127](https://cdn.treesir.pub/img/image-20210722160512127.png)

**选择需要修复的 blob storage**

![image-20210722160931972](https://cdn.treesir.pub/img/image-20210722160931972.png)

> 等待 `task` 任务的执行完毕



**再次执行 数据重建索引任务**

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# U/P: admin admin  默认密码
CONNECT PLOCAL:/nexus-data/db/component/ admin admin  
REBUILD INDEX *
REPAIR DATABASE --fix-graph
REPAIR DATABASE --fix-links
REPAIR DATABASE --fix-ridbags
REPAIR DATABASE --fix-bonsai
DISCONNECT
```



**重建完索引后，貌似又出现了新的问题**

> **查看一些包多显示 `404`**

![image-20210722100816785](https://cdn.treesir.pub/img/image-20210722100816785.png)



![image-20210722100943816](https://cdn.treesir.pub/img/image-20210722100943816.png)

![image-20210722100716967](https://cdn.treesir.pub/img/image-20210722100716967.png)



## 修复 依赖拉取、查看 `404` 问题

> 新建 重建私服的 `浏览`  & `搜索`  **task**

**创建相关 task**

![image-20210722162439277](https://cdn.treesir.pub/img/image-20210722162439277.png)

**选择对所有 仓库生效**

![image-20210722101653481](https://cdn.treesir.pub/img/image-20210722101653481.png)

![image-20210722162501026](https://cdn.treesir.pub/img/image-20210722162501026.png)

**启动重建任务**

![image-20210722101719109](https://cdn.treesir.pub/img/image-20210722101719109.png)

**等待任务结束**

![image-20210722101954917](https://cdn.treesir.pub/img/image-20210722101954917.png)

![image-20210722162536184](https://cdn.treesir.pub/img/image-20210722162536184.png)



**再次启动 pipeline 进行测试，还是报错，开启`TRACE` 级别日志进行查阅。**

> `TRACE` 日志级别建议，只在测试时开启，不要就进行关闭，内容实在太多

![image-20210722162741547](https://cdn.treesir.pub/img/image-20210722162741547.png)

![image-20210722162807053](https://cdn.treesir.pub/img/image-20210722162807053.png)

![image-20210722122658097](https://cdn.treesir.pub/img/image-20210722122658097.png)

>可以从相关的日志中看到  `Could not dispatch event AssetCreatedEvent`  报错。在社区寻找解决方案, `暂未找到合适的方法` 。

**只能继续，尝试重建一下索引看看能不能解决了**

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# U/P: admin admin  默认密码
CONNECT PLOCAL:/nexus-data/db/component/ admin admin  
REBUILD INDEX *
```

![image-20210722140438216](https://cdn.treesir.pub/img/image-20210722140438216.png)

> 执行 重建索引的时候直接就报错了。说发现重复的 `key`



**执行删除操作, 重启系统后 系统将自动完成索引的重建**

```bash
drop class browse_node
DISCONNECT

exit
```



**再次进行一次 数据库重建索引 及修复操作**

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# U/P: admin admin  默认密码
CONNECT PLOCAL:/nexus-data/db/component/ admin admin  
REBUILD INDEX *
REPAIR DATABASE --fix-graph
REPAIR DATABASE --fix-links
REPAIR DATABASE --fix-ridbags
REPAIR DATABASE --fix-bonsai
DISCONNECT

docker restart nexus3 
```

![image-20210722142059307](https://cdn.treesir.pub/img/image-20210722142059307.png)

> 再次执行时，已没有报错 出现重复的 key 了。



**重启后，查阅系统日志**

![image-20210722142718148](https://cdn.treesir.pub/img/image-20210722142718148.png)

> 可以从 日志中查看到，nexus 正在对 `存储库浏览树` 进行重建工作, 并且此时 已从 日志中看出 已重建完成了。



**再次观察后续的日志输出已无相关错误打印。尝试 触发 pipeline 看看**

![image-20210722152904777](https://cdn.treesir.pub/img/image-20210722152904777.png)

> 可以看到已 可正常拉取到 pypi 包依赖了。



# 参考链接

https://community.sonatype.com/t/unable-to-reach-metadata-file-instead-get-http-404-nof-found-error/5357

https://issues.sonatype.org/browse/NEXUS-21814?jql=text%20~%20%22AssetCreatedEvent%22

https://orientdb.com/docs/last/index.html?q=delete



# 总结

> 在经过此次事件后，感觉 nexus3 的 bug 还是非常多的，建议生产使用的版本，是使用社区反响较好的 bug 较少的版本。此次事件其实我在一年前的时候也是有碰到过，不过当时的解决的方式用的不是这一种，使用的方式是是 创建新的 blob 存储，而将有问题的 blog 私服进行迁移，这种方式比较简单，但是如果你 有很多个私服 并多使用在 一个 blob 存储上时，数据需要重新上传，迁移起来就比较麻烦和繁琐了。建议是 对 `不同环境` 和 `不同类型` 私服设置不同的 blob storage 来分散风险。 
