---
title: "部署 Nginx-Ingress 并配置暴露 kubernetes dashboard"
date: 2020-12-22T09:24:55+08:00
draft: false
tags: [ "helm","dashboard"]
tags_weight: 60
categories: ["k8s", "ingress"]
categories_weight: 30
keywords:
- centos7
- 部署
- 容器化
- nginx
- ingress
- dashboard
- kubernetes
- helm
description: "使用helm部署nginx ingrss，并配置将kubernetes dashboard暴露出来。"
---

# 环境说明
> [链接文档](https://www.treesir.pub/post/kubeadm-deploy-k8s1.9/)
## 软件版本说明
- helm: [v3.4.2]((https://github.com/helm/helm/tags))
- ingress: [v3.16.1](https://github.com/kubernetes/ingress-nginx/tags)

# Nginx Ingress
> [参考文档](https://kubernetes.github.io/ingress-nginx/)

## 安装
```bash
wget https://get.helm.sh/helm-v3.4.2-linux-amd64.tar.gz

tar xf helm-v3.4.2-linux-amd64.tar.gz \
&& cp linux-amd64/helm /usr/local/bin/
```

## 添加helm命令补全
```
helm completion bash \
&& helm completion bash > /etc/bash_completion.d/helm
```

## 添加 ingress repo
```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx \
&& helm repo update \
&& helm repo list 
```

## 修改(查看)默认配置
> 首先我们先获取一下默认的配置文件
```bash
helm show values ingress-nginx/ingress-nginx # 查看后大多不需要修改，保留为默认即可
```

## 生成配置部署文件
> 创建 deploy-yaml.yaml 文件，包涵安装时覆盖默认中的配置。
> 
```yaml
controller:
  dnsPolicy: ClusterFirstWithHostNet
  hostNetwork: true
  publishService:  # hostNetwork 模式下设置为false，通过节点IP地址上报ingress status数据
    enabled: false
  kind: DaemonSet
  nodeSelector: 
    role: lb  # 节点亲和性，只在拥有 "rele=lb" 的节点上部署
  service:  # HostNetwork 模式不需要创建 service
    enabled: false
defaultBackend:
  enabled: true
```

## 安装
```bash
kubectl create ns ingress-nginx  # 创建部署的命名空间

kubectl label nodes node01 role=lb  # 应为我们添加了节点亲和性，还要给节点添加一个标签。

helm upgrade --install ingress -f ./deploy-values.yaml -n ingress-nginx ingress-nginx/ingress-nginx

watch kubectl get pod -n ingress-nginx # 等待容器启动完成
```
> 启动完成后我们访问一下节点的ip，显示 "default backend - 404"即是正常。

![image-20201222100233938](https://cdn.treesir.pub/images/2020/12/22/image-202012221002339384200893226980fd7.png)


## 测试效果
> 创建一个nginx的 deployment对象

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-nginx
spec:
  selector:
    matchLabels:
      app: my-nginx
  template:
    metadata:
      labels:
        app: my-nginx
    spec:
      containers:
      - name: my-nginx
        image: nginx
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: my-nginx
  labels:
    app: my-nginx
spec:
  ports:
  - port: 80
    protocol: TCP
    name: http
  selector:
    app: my-nginx
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: my-nginx
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host: ngdemo.treesir.pub  # 使用的域名映射，当访问 "ngdemo.treesir.pub" 域名时转发至后端的pod
    http:
      paths:
      - path: /
        backend:
          serviceName: my-nginx
          servicePort: 80
```

```bash
kubectl create -f ./nginx.yaml
```



> 修改 host文件后，测试访问一下。( `Uninx or Linux` 修改 /etc/hosts, `Windows` 修改路径为：C:\Windows\System32\drivers\etc\hosts)

![image-20201222101535793](https://cdn.treesir.pub/images/2020/12/22/image-20201222101535793.png)

> 客户端使用Nginx Ingress访问后端pod的全流程图解析，(图片转至 [优点知识](https://youdianzhishi.com/web))

![ingress controller workflow](https://cdn.treesir.pub/img/ingress-controller-workflow.png)

# 暴露 dashboard

```yaml
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: ingress-dashboard
  namespace: kubernetes-dashboard
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
spec:
  tls:
  - hosts:
    - dashboard.treesir.pub
    secretName: kubernetes-dashboard-certs
  rules:
  - host: dashboard.treesir.pub
    http:
      paths:
      - path: /
        backend:
          serviceName: kubernetes-dashboard
          servicePort: 443
```


```bash
kubectl create -f dashboard-ingress.yaml
```

> `修改host` 文件后测试访问

![image-20201222102831834](https://cdn.treesir.pub/images/2020/12/22/image-20201222102831834.png)
