---
title: "Gitlab 和 jira 之间的集成"
date: 2021-04-09T15:52:44+08:00
draft: false
tags: [ "jira", "gitlab"]
tags_weight: 80
categories: ["devops"]
categories_weight: 80
keywords:
- Gitlab
- git
- jira
- issue
description: "使用 Gitlab 时，git commit 和 jira issue id 的集成"
---

# 环境说明

> Gitlab-ce 版本： 13.10.2
>
> Jira 版本：v8.13.4



## Jira

## 获取 工作流结束 id

![image-20210412165030920](https://cdn.treesir.pub/img/image-20210412165030920.png)

![image-20210412165137491](https://cdn.treesir.pub/img/image-20210412165137491.png)

**获取到工作流 结束 id `21`**

![image-20210412165307421](https://cdn.treesir.pub/img/image-20210412165307421.png)





# Gitlab

## 设置全局与 jira 集成

![image-20210412170113952](https://cdn.treesir.pub/img/image-20210412170113952.png)

![image-20210412170131429](https://cdn.treesir.pub/img/image-20210412170131429.png)

![image-20210412170156662](https://cdn.treesir.pub/img/image-20210412170156662.png)

## 测试效果

> 找到需要关联 issue id

![image-20210412170330710](https://cdn.treesir.pub/img/image-20210412170330710.png)

**编辑 文件 commit时输入 `issue id 空格 message`**

![image-20210412170454342](https://cdn.treesir.pub/img/image-20210412170454342.png)

![image-20210412170615257](https://cdn.treesir.pub/img/image-20210412170615257.png)

> 可以看到对应的 jira issue 页面已经可以看到对应的效果了
>
> 更改具体的操作 可以参考一下这个 [**文档**](https://docs.gitlab.com/ee/user/project/integrations/jira_server_configuration.html)



# 总结

> Jira 与 Gitlab 之间的集成后，可以实时了解到此 issue 目前的状态信息，可以合理提升人员之间的信息沟通。如配合 Gitlab 中服务端的 pre-receive webhook 强制要求用户在每次 commit 时添加对应的 issue id，那么效果更佳。后续有时间的话，我会出相关的配置教程，下面记录一下之前项目踩坑的地方。
>
> - Gitlab 连接时使用的 jira 用户权限问题
>
>   ![image-20210412172154863](https://cdn.treesir.pub/img/image-20210412172154863.png)
>   ![image-20210412172256543](https://cdn.treesir.pub/img/image-20210412172256543.png)
