---
title: "记录一次 JupyterLab 插件打包问题的修复"
date: 2021-07-20T17:04:26+08:00
draft: false
tags: [ "jupyterlab","jenkins"]
tags_weight: 80
categories: ["k8s","devops"]
categories_weight: 80
keywords:
- jupyterlab
- jenkins
- k8s
- devops
- 打包
- 未生成包
- 未正常安装
- 安装失败
description: "JupyterLab 插件使用 jenkins pipeline 打包错误问题修复记录。"
---



# 背景

> 在所关联项目 的 jenkins pipeline ci 流水线中，开发告诉我 生成的 pip 包中未有生成相关的 `labextension` 依赖文件夹，导致 pip 包虽然是安装成功了，但仍是一个不可用的状态。开发人员告诉我在 `linux` & `windows` 环境下 均是可以正常生成相关依赖文件夹的，问我是不是哪里配置有点问题，思索了一下 和他说会不会是 jenkins slave 使用的 是 `容器` 的原因？给了他一个启动命令，测试在容器里面生成一下包看看，启动命令如下所示:
>
> ```bash
> docker run -it --rm --name test --entrypoint /bin/bash idocker.io/base/jenkins/inbound-agent:4.3-8-jdk11
> ```
>
> 后续经过开发一番操作后，仍然是可以生成正常依赖包，这就让我感觉有点奇怪了，于是就有了此篇文档，关于我对此问题的逐一定位。

![image-20210720165315715](https://cdn.treesir.pub/img/image-20210720165315715.png)



# 逐一排查定位

## 启动容器，尝试执行相关命令

> 容器启动命令，使用和上面一样的命令，覆盖默认的 `entrypoint` 语法，改用 `/bin/bash` ，模拟 jenkins slave 真实的打包过程。

![image-20210720160534876](https://cdn.treesir.pub/img/image-20210720160534876.png)

使用 jenkins pipeline 后的 日志输出

![image-20210720160740289](https://cdn.treesir.pub/img/image-20210720160740289.png)



> 观察对应的日志输出，在执行 `python3 setup.py bdist_wheel ` 后有出现 `jlpm install` 对应的命令才能生成出正常可使用的 pip 依赖包。



## 测试 pipeline (一)

> 此 pipeline 目的是检查，相关的 `版本是不是一致`。

```bash
#!groovy
@Library('jenkinslibrary@master') _

def todingtalk = new org.devops.todingtalk()
def tools = new org.devops.tools()

pipeline{
    agent {
        kubernetes{
            cloud 'rancher-dev'
            yaml libraryResource('jenkins/slave.yaml')
        }
    }

    stages{
        stage('Clean \u2756') {
            steps {
                script{
                    cleanWs()
                }
            }
        }
        
        stage('GetBuildUser \u2756') {
            steps {
                script {
                    tools.GetBuildUser()
                    sh '''
                        jlpm --version
                        jupyter --version
                    '''
                    
                    println "${currentBuild.durationString.replace(' and counting', '')}"
                }
            }
        }
    }
}
```

![image-20210720161136416](https://cdn.treesir.pub/img/image-20210720161136416.png)

![image-20210720174127367](https://cdn.treesir.pub/img/image-20210720174127367.png)

> 比对相关软件版本，打印的输出是一样的， `依赖版本` & `jenkins slave 相关镜像` 的使用应该是没有什么问题的。



## 测试 pipeline (二)

> 检查版本一致后，尝试再次进行一次打包

```bash
#!groovy
@Library('jenkinslibrary@master') _

def todingtalk = new org.devops.todingtalk()
def tools = new org.devops.tools()

pipeline{
    agent {
        kubernetes{
            cloud 'rancher-dev'
            yaml libraryResource('jenkins/slave.yaml')
        }
    }

    stages{
        stage('Clean \u2756') {
            steps {
                script{
                    cleanWs()
                }
            }
        }
        
        stage("CheckOut \u2756"){
            steps{
                script{
                    tools.PrintMes("代码检出","green")
                    checkout([
                        $class: 'GitSCM', 
                        branches: [[name: "dev"]],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [], 
                        submoduleCfg: [], 
                        userRemoteConfigs: [
                            [credentialsId: '4852cf10-0ced-4b88-bdbe-22edac8f4de0',
                            url: "http://glb.ac.com/diting/jupyter-resource-usage-server.git"
                            ]
                        ]
                    ])
                }
            }
        }
        
        stage('GetBuildUser \u2756') {
            steps {
                script {
                    tools.GetBuildUser()
                    sh '''
                        jlpm --version
                        jupyter --version
                        
                        rm -rf .git \
                        && python3 setup.py bdist_wheel 
                    '''
                    
                    println "${currentBuild.durationString.replace(' and counting', '')}"
                }
            }
        }
    }
}
```

![image-20210720161700281](https://cdn.treesir.pub/img/image-20210720161700281.png)

> 日志输出可以看到，一样的效果没有执行 `jlpm`  相关子命令，确认了 镜像 和 软件版本不是问题因素。



## 测试 pipeline (三)

> 尝试 使用 `nohup` 进行后台运行，看看是不是 那个 `终端参数设置错误` 问题导致。

```bash
#!groovy
@Library('jenkinslibrary@master') _

def todingtalk = new org.devops.todingtalk()
def tools = new org.devops.tools()

pipeline{
    agent {
        kubernetes{
            cloud 'rancher-dev'
            yaml libraryResource('jenkins/slave.yaml')
        }
    }

    stages{
        stage('Clean \u2756') {
            steps {
                script{
                    cleanWs()
                }
            }
        }
        
        stage("CheckOut \u2756"){
            steps{
                script{
                    tools.PrintMes("代码检出","green")
                    checkout([
                        $class: 'GitSCM', 
                        branches: [[name: "dev"]],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [], 
                        submoduleCfg: [], 
                        userRemoteConfigs: [
                            [credentialsId: '4852cf10-0ced-4b88-bdbe-22edac8f4de0',
                            url: "http://glb.ac.com/diting/jupyter-resource-usage-server.git"
                            ]
                        ]
                    ])
                }
            }
        }
        
        stage('GetBuildUser \u2756') {
            steps {
                script {
                    tools.GetBuildUser()
                    sh '''
                        jlpm --version
                        jupyter --version
                        
                        rm -rf .git
                        nohup python3 setup.py bdist_wheel >> nohup.out 2>&1 &
                        sleep 10 \
                        && head -n 100 nohup.out
                    '''
                    
                    println "${currentBuild.durationString.replace(' and counting', '')}"
                }
            }
        }
    }
}
```

![image-20210720162943941](https://cdn.treesir.pub/img/image-20210720162943941.png)

> 后台的日志输出中，还是没有运行 `jlpm` 相关子命令，排除了 终端参数设置错误的问题。但是仔细观察相关的日志后，发现 在打包过程中在读取 `MANIFEST.in` 的逻辑时，是有几个警告信息，是不是和 `MANIFEST.in `里面的类容有关系呢？

**查看对应的 MANIFEST.in 文件内容**

![image-20210720163240905](https://cdn.treesir.pub/img/image-20210720163240905.png)

> 观察 `MANIFEST.in`  这个文件内容，看到此文件中有 关联 `.git` 的逻辑说明，是不是 pipeline 中有做 `rm -rm .git`步骤，从而影响到打包了呢？  



## 测试 pipeline (四)

> 将 `rm -rf .git` 步骤去掉，再次执行一下打包逻辑。

```bash
#!groovy
@Library('jenkinslibrary@master') _

def todingtalk = new org.devops.todingtalk()
def tools = new org.devops.tools()

pipeline{
    agent {
        kubernetes{
            cloud 'rancher-dev'
            yaml libraryResource('jenkins/slave.yaml')
        }
    }

    stages{
        stage('Clean \u2756') {
            steps {
                script{
                    cleanWs()
                }
            }
        }
        
        stage("CheckOut \u2756"){
            steps{
                script{
                    tools.PrintMes("代码检出","green")
                    checkout([
                        $class: 'GitSCM', 
                        branches: [[name: "dev"]],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [], 
                        submoduleCfg: [], 
                        userRemoteConfigs: [
                            [credentialsId: '4852cf10-0ced-4b88-bdbe-22edac8f4de0',
                            url: "http://glb.ac.com/diting/jupyter-resource-usage-server.git"
                            ]
                        ]
                    ])
                }
            }
        }
        
        stage('GetBuildUser \u2756') {
            steps {
                script {
                    tools.GetBuildUser()
                    sh '''
                        jlpm --version
                        jupyter --version
                        
                        python3 setup.py bdist_wheel
                    '''
                    
                    println "${currentBuild.durationString.replace(' and counting', '')}"
                }
            }
        }
    }
}
```

![image-20210720163705281](https://cdn.treesir.pub/img/image-20210720163705281.png)

> 此时再次查看 对应流水线的日志输出时，惊喜的发现 已有 `jlpm ` 命令执行记录的输出。为验证 包的完整性，我将对应的 `whl` 包进行了下载并使用工具进行解压查看，看到已正常生成对应的 `labextension` 依赖文件夹。后续修改了 线上 pipeline 逻辑后，rebuild 后，让开发进行检查后，是正常的了，此致问题解决。
>
> ![image-20210720165145369](https://cdn.treesir.pub/img/image-20210720165145369.png)



# 总结

> 在处理技术问题的过程中，个人感觉，第一个是需要多多观察相关程序的日志输出，其实很多解决问题的方法多在日志中有表现，很多人就是不会去检查日志。第二个是要了解其相关技术的 `实现原理`。做技术的光知道怎么用是不够的，实现原理也需要进行了解才行，这样碰到稀奇古怪的问题后，能够洞察分析问题的本质，精准定位并解决。

## toDo
