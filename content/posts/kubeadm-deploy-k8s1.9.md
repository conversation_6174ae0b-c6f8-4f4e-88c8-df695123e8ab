---
title: "Kubeadm 部署 kubernetes-v1.19.x 集群"
date: 2020-12-21T13:19:31+08:00
draft: false
tags: [ "kubeadm", "v1.19.x", "install"]
tags_weight: 80
categories: ["k8s"]
categories_weight: 80
keywords:
- centos7
- kubeadm
- 部署
- 容器化
- deploy
- docker
- k8s
- kubernetes
description: "使用kubeadm部署kubernetes-v1.19.x集群"
---
# 系统环境说明
- 使用操作系统: Centos-7.9.2009
- 操作系统内核版本: 4.4.248 (`lt`)
- Docker容器版本: 18.09.9
- Kubeadm 版本: 1.19.6
- 节点说明:
  - master01: ************ ,*************
  - node01: ************ ,*************
  - (备注: 为每个节点配置了两个ip，对外服务网段为: `***********/24`, 集群内部通讯: `************/24`)

# OS 准备
## 系统初始化
`确保已将selinux关闭`
> 请 [参考文档](https://www.treesir.pub/post/centos-init-config/)

## 关闭swap
> kubernetes `不支持` 使用交换空间
```bash
swapoff -a
sed -i 's/.*swap.*/#&/' /etc/fstab
```

## 加载ivps模块
> 这里加载模块其目的是为了让后面的KubeProxy组件使用`ivps模式`来提高集群性能，kube-proxy它的作用是转发服务之间的流量（通过群集IP和节点端口）负载均衡到正确的后端Pod。Kube-proxy可以选择在三种模式中的之一运行，每种模式都使用不同的技术实现，它们分别是：`userspace` ，`iptables` 和 `IPVS`,kubeadm 默认使用模式为iptables, 详细文档请 [**参考**](https://www.projectcalico.org/comparing-kube-proxy-modes-iptables-or-ipvs/)。   

```bash
cat > /etc/sysconfig/modules/ipvs.modules <<EOF
#!/bin/bash
modprobe -- ip_vs
modprobe -- ip_vs_rr
modprobe -- ip_vs_wrr
modprobe -- ip_vs_sh
modprobe -- nf_conntrack_ipv4
EOF

chmod 755 /etc/sysconfig/modules/ipvs.modules \
&& bash /etc/sysconfig/modules/ipvs.modules \
&& lsmod | grep -e ip_vs -e nf_conntrack_ipv4

# 配置完成之后最好重启一下系统再检查是否有生效
reboot 
lsmod | grep -e ip_vs -e nf_conntrack_ipv4 # 重启完成后执行
```
## 修改主机名称
```bash
hostnamectl set-hostname xxx  # xxx为你要修改的主机名称 示例: hostnamectl set-hostname master01
```

## 配置修改hosts文件
```bash
# 每个节点多需要执行一下

cat >> /etc/hosts << EOF
************* master01
************* node01
EOF

# 我这里将域名解析至内部通讯ip，更具自身环境修改即可
```

## 配置节点之间免密登录
```bash
ssh-keygen # 执行此命令后一路回车即可

# 将生成的公钥copy至目标主机完成免密
ssh-copy-id node01  
ssh-copy-id master01

# 如有多个节点时可使用此方法
for n in `seq -w 01 04`;do ssh-copy-id node$n;done
for n in `seq -w 01 03`;do ssh-copy-id master$n;done

# 非交互式完成免密 (依赖使用sshpass工具，可使用yum安装)
for i in `seq 1 3`;do sshpass -p '123456' ssh-copy-id -o StrictHostKeyChecking=no node"$i".com;done
for i in `seq 1 3`;do sshpass -p '123456' ssh-copy-id -o StrictHostKeyChecking=no master"$i".com;done
```

## 配置时间同步
```bash
yum install -y ntp # 客户端形式使用
/sbin/ntpdate -u ntp1.aliyun.com  # 这里使用aliyun的ntp服务器，如有内网ntp替换使用即可

# 添加至定时任务中
crontab -e  # 将下面的命令添加至定时任务队列中
*/10 * * * * /sbin/ntpdate -u ntp1.aliyun.com >/dev/null 2>&1

systemctl enable crond # 检查定时服务是否自启
```

# kubeadm 安装部署集群
## 配置aliyun的kubernetes源
```bash
cat <<EOF > /etc/yum.repos.d/kubernetes.repo
[kubernetes]
name=Kubernetes
baseurl=https://mirrors.aliyun.com/kubernetes/yum/repos/kubernetes-el7-x86_64/
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://mirrors.aliyun.com/kubernetes/yum/doc/yum-key.gpg https://mirrors.aliyun.com/kubernetes/yum/doc/rpm-package-key.gpg
EOF

yum makecache fast  # 执行后确认密钥，输入“y”
```
## 执行安装特点版本
```bash
yum list kubeadm --showduplicates | sort -r # 打印查看所有 kubeadm 版本

yum install -y kubeadm-1.19.6 kubelet-1.19.6 kubectl-1.19.6

systemctl enable kubelet  # 安装完成后 添加kebelet服务自启

```
## 配置 kubectl 命令补全
```
yum install -y bash-completion
source /usr/share/bash-completion/bash_completion
source <(kubectl completion bash)
echo "source <(kubectl completion bash)" >> ~/.bashrc
```

## 生成配置文件部署（`master`）
```bash
mkdir workspace \
&& cd workspace 

kubeadm config print init-defaults >> kubeadm-init.yaml
```

### 修改后的配置文件展示
```yaml
apiVersion: kubeadm.k8s.io/v1beta2
bootstrapTokens:
- groups:
  - system:bootstrappers:kubeadm:default-node-token
  token: abcdef.0123456789abcdef
  ttl: 24h0m0s
  usages:
  - signing
  - authentication
kind: InitConfiguration
localAPIEndpoint:
  advertiseAddress: ************* # master ip
  bindPort: 6443
nodeRegistration:
  criSocket: /var/run/dockershim.sock
  name: master01
  taints:
  - effect: NoSchedule
    key: node-role.kubernetes.io/master
---
apiServer:
  timeoutForControlPlane: 4m0s
apiVersion: kubeadm.k8s.io/v1beta2
certificatesDir: /etc/kubernetes/pki
clusterName: kubernetes
controllerManager: {}
dns:
  type: CoreDNS
etcd:
  local:
    dataDir: /var/lib/etcd
imageRepository: k8s.gcr.io # 拉取镜像的地址，可设置为aliyun的地址"registry.cn-hangzhou.aliyuncs.com/google_containers"，我这里使用默认。
kind: ClusterConfiguration
kubernetesVersion: v1.19.6
networking:
  dnsDomain: cluster.local
  podSubnet: **********/16  # 默认网段为: "**********/16"
  serviceSubnet: *********/12
scheduler: {}

---
apiVersion: kubeproxy.config.k8s.io/v1alpha1  # 开启 ipvs 
kind: KubeProxyConfiguration
mode: "ipvs"
```

## 根据配置文件预拉取镜像 (`master`)
```bash
cd workspace \
&& kubeadm config images pull --config kubeadm-init.yaml
```

## 集群初始化 (`master`)
```bash
kubeadm init --config kubeadm-init.yaml

# 等待初始化完成后 配置kubectl
mkdir -p $HOME/.kube
cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
chown $(id -u):$(id -g) $HOME/.kube/config
```


## 清理节点

如果在集群安装过程中，遇到一些不可描述的问题，我们可以使用下面的命令进行重置节点

```bash
kubeadm reset
ifconfig cni0 down && ip link delete cni0
ifconfig flannel.1 down && ip link delete flannel.1
rm -rf /var/lib/cni/

# 清理Iptables表
## 注意：如果节点Iptables有特殊配置，以下命令请谨慎操作
sudo iptables --flush
sudo iptables --flush --table nat
sudo iptables --flush --table filter
sudo iptables --table nat --delete-chain
sudo iptables --table filter --delete-chain
systemctl restart docker
```

## 配置node加入集群 (`node`)

![image-20201221154559403](https://cdn.treesir.pub/images/2020/12/21/image-20201221154559403.png)

> Copy master 初始化后打印的语句，在`node节点`中执行
```bash
kubeadm join *************:6443 --token abcdef.0123456789abcdef  \
--discovery-token-ca-cert-hash sha256:e390239dde11e9657a2418f309728c422da1823ab6eba1ec2e433c3783eba46
```

![image-20201221154949889](https://cdn.treesir.pub/images/2020/12/21/image-20201221154949889.png)


## 部署网络插件 `flannel `(`master`)

```bash
# Kubernetes v1.17+ 集群执行执行上面这条语句即可，但是由于我修改了podSubnet的默认地址，所有部署文件也需要相应的修改一下，就要执行第二条语句来将网段替换一下了。
kubectl apply -f  https://raw.githubusercontent.com/coreos/flannel/v0.13.0/Documentation/kube-flannel.yml

curl https://raw.githubusercontent.com/coreos/flannel/v0.13.0/Documentation/kube-flannel.yml|sed 's#**********/16#**********/16#g' | kubectl apply -f -

kubectl get pod -n kube-system  # 检查pod是否启动
NAME                               READY   STATUS    RESTARTS   AGE
coredns-f9fd979d6-45wgz            1/1     Running   0          15m
coredns-f9fd979d6-vphjf            1/1     Running   0          15m
etcd-master01                      1/1     Running   0          15m
kube-apiserver-master01            1/1     Running   0          15m
kube-controller-manager-master01   1/1     Running   0          15m
kube-flannel-ds-fxl9g              1/1     Running   0          2m8s
kube-flannel-ds-gqh5s              1/1     Running   0          2m8s
kube-proxy-bqhn4                   1/1     Running   0          15m
kube-proxy-pp2jd                   1/1     Running   0          10m
kube-scheduler-master01            1/1     Running   0          15m
```

## kubelet 添加额外参数
> `不需要执行的操作，且做记录使用`
```bash
service kubelet status # 查看服务信息
Drop-In: /usr/lib/systemd/system/kubelet.service.d  #找到关键行

# 进入此目录
/usr/lib/systemd/system/kubelet.service.d

# 查看这个配置文件中的内容
cat 10-kubeadm.conf
EnvironmentFile=-/etc/sysconfig/kubelet  # 修改这个配置文件中内容即可

# 修改节点的内网通讯IP
PRIVATE_IP=************
echo "KUBELET_EXTRA_ARGS=--node-ip=$PRIVATE_IP" > /etc/sysconfig/kubelet
systemctl daemon-reload
systemctl restart kubelet
```

# 部署dashboard
> 目前市面上Dashboard种类繁多，如：原生的 [dashboard](https://kubernetes.io/zh/docs/tasks/access-application-cluster/web-ui-dashboard/)、[rancher](https://rancher.com/)、[lens](https://github.com/lensapp/lens)、[kubespape](https://kubesphere.io/)、[Kuboard](https://kuboard.cn/)等等，因为项目缘故对rancher用的比较多，我们这里就演示部署rancher进行管理集群吧。

## rancher 单机部署
- Rancher github [地址](https://hub.docker.com/r/rancher/rancher)
```bash
docker run -d --restart=unless-stopped \
  --name rancher \
  -p 8080:80 -p 8443:443 \
  --privileged \
  rancher/rancher:v2.4.8

docker logs -f --tail 100 rancher # 查看日志等待启动完成
```
## rancher 导入集群

### 添加集群

![image-20201221163010517](https://cdn.treesir.pub/images/2020/12/21/image-20201221163052668.png)

![image-20201221163052668](https://cdn.treesir.pub/images/2020/12/21/image-20201221163052668.png)

![image-20201221163143449](https://cdn.treesir.pub/images/2020/12/21/image-20201221163143449.png)

### 复制命令在kubeadm 管理节点中执行

![image-20201221163213606](https://cdn.treesir.pub/images/2020/12/21/image-20201221163213606.png)

> 发现执行下面语句后，ranche r集群显示异常。
>
> - 修复此问题: [参考文档](https://www.treesir.pub/post/rancher-import-cluster-fix/)
>
> ```bash
> curl --insecure -sfL https://192.168.8.66:8443/v3/import/jzrpkrzjpjgv9q5gqqg8hlhm29ldb5gv9mn4xpg8cxgfh5zg4mfs4h.yaml|sed 's#rbac.authorization.k8s.io/v1beta1#rbac.authorization.k8s.io/v1#g' |kubectl apply -f -
> ```
>
> ![image-20201221164538840](https://cdn.treesir.pub/images/2020/12/21/image-20201221164538840.png)



## 部署 `原生` dashboard
> [**参考文档**](https://github.com/kubernetes/dashboard/tree/master/docs)

```bash
kubectl apply -f https://raw.githubusercontent.com/kubernetes/dashboard/v2.0.1/aio/deploy/recommended.yaml

watch kubectl get pod -n kubernetes-dashboard  # 等待容器启动完成
```

### 临时使用kubectl 将 dashboard 代理出来

> 后面我会更新使用`ingress` 的方法
```bash
kubectl port-forward -n kubernetes-dashboard service/kubernetes-dashboard 8080:443 --addss 0.0.0.0
```

### 获取用户 Token
```bash
kubectl get secret -n kubernetes-dashboard|grep dashboard-token|awk '{print $1}'|xargs -I {} kubectl -n kubernetes-dashboard get secret {} -o jsonpath={.data.token}|base64 -d
```
> 访问地址为 `https://<kubectl-ip>:8080`, 注意如果chrome浏览器访问不安全的https链接没有继续访问的操作的话，可以尝试使用其他浏览器如: firefox，当然好像也可以降低chrome浏览器的安全级别来解决此问题

![image-20201221201443582](https://cdn.treesir.pub/images/2020/12/21/image-20201221201443582.png)

**登录成功后发现抛出大量权限不足错误**
![image-*****************](https://cdn.treesir.pub/images/2020/12/21/image-*****************.png)

### 解决方法-重新绑定默认用户为管理员(`生产不推荐`)

创建 `admin-role.yaml` 配置文件内容如下
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubernetes-dashboard
  namespace: kubernetes-dashboard
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - kind: ServiceAccount
    name: kubernetes-dashboard
    namespace: kubernetes-dashboard
```

```bash
kubectl delete -f ./admin-role.yaml \
&& kubectl create -f ./admin-role.yaml
```

**再次刷新不再报错了，且集群信息显示正常**

![image-*****************](https://cdn.treesir.pub/images/2020/12/21/image-*****************.png)
